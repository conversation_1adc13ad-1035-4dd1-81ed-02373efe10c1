# Nginx Dockerfile - PRODUÇÃO
FROM nginx:alpine

# Instalar certbot para SSL
RUN apk add --no-cache certbot certbot-nginx

# Copiar configuração de produção
COPY nginx.prod.conf /etc/nginx/nginx.conf

# Criar diretórios necessários
RUN mkdir -p /var/log/nginx \
    && mkdir -p /var/www/certbot \
    && mkdir -p /etc/letsencrypt

# Copiar arquivos estáticos do frontend (será copiado pelo build)
COPY --from=frontend /app/dist /usr/share/nginx/html

# Expor portas 80 e 443
EXPOSE 80 443

# Script de inicialização para SSL
COPY docker/nginx-entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Comando padrão
ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
