version: '3.8'

services:
  # Nginx - PRODUÇÃO
  nginx:
    build:
      context: .
      dockerfile: nginx.prod.Dockerfile
    ports:
      - '80:80'
      - '443:443'
    networks:
      - app-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend React + TypeScript (Vite) - PRODUÇÃO
  frontend:
    build:
      context: ./frontend
      target: production
    networks:
      - app-network
    volumes:
      - frontend_dist:/app/dist
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://blueprintblog.tech/api
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:5173 || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend Rust - PRODUÇÃO
  backend:
    build:
      context: ./backend
      target: production
    # 🔒 SEM exposição direta - apenas via nginx
    networks:
      - app-network
    environment:
      - DATABASE_URL=postgresql://postgres.nbzovkwvrqlxqpdpkvql:<EMAIL>:6543/postgres
      # - REDIS_URL=redis://redis:6379  # Redis desabilitado
      - RUST_ENV=production
      - RUST_LOG=info
      - LOG_LEVEL=info
      - LOG_CONSOLE=false
      - LOG_FILE=true
      - LOG_JSON=true
      - LOG_DIR=./logs
      - LOG_MAX_FILES=30
      - LOG_MAX_AGE_DAYS=30
      - HOST=0.0.0.0
      - PORT=3001
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    # depends_on:
    #   - redis  # Redis desabilitado
    # 🔧 Limites de recursos para produção
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:3001/health || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache - DESABILITADO TEMPORARIAMENTE
  # redis:
  #   image: redis:7-alpine
  #   networks:
  #     - app-network
  #   volumes:
  #     - redis_data:/data
  #   command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ['CMD', 'redis-cli', 'ping']
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 40s

  # Prometheus - Monitoramento
#  prometheus:
#    image: prom/prometheus:latest
#    ports:
#      - '9090:9090'
#    networks:
#      - app-network
#    volumes:
#      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
#      - prometheus_data:/prometheus
#    command:
#      - '--config.file=/etc/prometheus/prometheus.yml'
#      - '--storage.tsdb.path=/prometheus'
#      - '--web.console.libraries=/etc/prometheus/console_libraries'
#      - '--web.console.templates=/etc/prometheus/consoles'
#      - '--storage.tsdb.retention.time=200h'
#      - '--web.enable-lifecycle'
#    restart: unless-stopped

# Grafana - Dashboard
#  grafana:
#    image: grafana/grafana:latest
#    ports:
#      - '3002:3000'
#    networks:
#      - app-network
#    volumes:
#      - grafana_data:/var/lib/grafana
#      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
#    environment:
#      - GF_SECURITY_ADMIN_USER=admin
#      - GF_SECURITY_ADMIN_PASSWORD=admin123
#      - GF_USERS_ALLOW_SIGN_UP=false
#    restart: unless-stopped

# Rede de produção
networks:
  app-network:
    driver: bridge

# Volumes de produção
volumes:
  frontend_dist:
  #  redis_data:
  backend_logs:
  backend_uploads:
#  prometheus_data:
#  grafana_data:
