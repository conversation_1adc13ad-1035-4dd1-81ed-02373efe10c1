{"name": "blueprint-blog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "format": "prettier --write .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "buffer": "^6.0.3", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "framer-motion": "^12.15.0", "gray-matter": "^4.0.3", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.2", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.1", "react-syntax-highlighter": "^15.6.1", "reading-time": "^1.5.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "web-vitals": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/dompurify": "^3.0.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.5"}}