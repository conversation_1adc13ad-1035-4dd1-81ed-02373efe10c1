import clsx from 'clsx';
import { Code2, Menu, X } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import LanguageSelector from '../ui/LanguageSelector';
import ThemeToggle from '../ui/ThemeToggle';

interface NavigationItem {
  name: string;
  href: string;
  icon: string;
}

function Navbar() {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  const navigation: NavigationItem[] = [
    { name: t('nav.home', 'Home'), href: '/', icon: '🏠' },
    { name: t('nav.blog', 'Blog'), href: '/blog', icon: '📝' },
    { name: t('nav.projects', 'Projects'), href: '/projects', icon: '🚀' },
    { name: t('nav.devlog', 'Devlog'), href: '/devlog', icon: '📜' },
    { name: t('nav.about', 'About'), href: '/about', icon: '👾' },
    { name: t('nav.themes', 'Themes'), href: '/themes', icon: '🎨' },
    { name: t('nav.contact', 'Contact'), href: '/contact', icon: '📬' },
  ];

  const isActive = (href: string): boolean => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <nav className="fixed top-0 w-full z-50 bg-adaptive-card/90 backdrop-blur-md border-b border-adaptive">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 group">
            <Code2 className="h-8 w-8 text-adaptive-blue group-hover:animate-glow" />
            <span className="text-xl font-bold text-gradient glow-text">BLUEPRINT</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={clsx(
                  'flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-300',
                  isActive(item.href)
                    ? 'text-adaptive-blue bg-adaptive-blue/10 border border-adaptive-blue/20'
                    : 'text-adaptive-secondary hover:text-adaptive-blue hover:bg-adaptive-blue/5'
                )}
              >
                <span>{item.icon}</span>
                <span>{item.name}</span>
              </Link>
            ))}
            <LanguageSelector className="ml-1" />
            <ThemeToggle className="ml-1" />
          </div>

          {/* Mobile menu button and theme toggle */}
          <div className="md:hidden flex items-center space-x-2">
            <LanguageSelector />
            <ThemeToggle />
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-adaptive-secondary hover:text-adaptive-blue p-2"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden bg-adaptive-card border-t border-adaptive">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                onClick={() => setIsOpen(false)}
                className={clsx(
                  'flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-all duration-300',
                  isActive(item.href)
                    ? 'text-adaptive-blue bg-adaptive-blue/10 border border-adaptive-blue/20'
                    : 'text-adaptive-secondary hover:text-adaptive-blue hover:bg-adaptive-blue/5'
                )}
              >
                <span>{item.icon}</span>
                <span>{item.name}</span>
              </Link>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
}

export default Navbar;
