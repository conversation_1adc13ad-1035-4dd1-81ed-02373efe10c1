import { Github, Linkedin, Mail, Twitter, LucideIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface SocialLink {
  name: string;
  href: string;
  icon: LucideIcon;
}

const Footer: React.FC = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  const socialLinks: SocialLink[] = [
    {
      name: 'GitHub',
      href: 'https://github.com',
      icon: Github,
    },
    {
      name: 'LinkedIn',
      href: 'https://linkedin.com',
      icon: Linkedin,
    },
    {
      name: 'Twitter',
      href: 'https://twitter.com',
      icon: Twitter,
    },
    {
      name: 'Email',
      href: 'mailto:<EMAIL>',
      icon: Mail,
    },
  ];

  return (
    <footer className="bg-adaptive-card border-t border-adaptive-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Copyright */}
          <div className="text-adaptive-text-secondary text-sm">
            <p>{t('footer.copyright', { year: currentYear })}</p>
          </div>

          {/* Social Links */}
          <div className="flex items-center space-x-6">
            {socialLinks.map((link) => {
              const Icon = link.icon;
              return (
                <a
                  key={link.name}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-adaptive-text-secondary hover:text-adaptive-accent-blue transition-colors duration-300"
                  aria-label={link.name}
                >
                  <Icon className="h-5 w-5" />
                </a>
              );
            })}
          </div>

          {/* Manifesto */}
          <div className="text-adaptive-text-secondary text-sm text-center md:text-right">
            <p className="text-gradient font-mono">"O mapa. A planta. O rascunho da vida dev."</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
