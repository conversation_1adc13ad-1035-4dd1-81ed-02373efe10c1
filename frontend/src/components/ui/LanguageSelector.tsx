import { AnimatePresence, motion } from 'framer-motion';
import { Check, ChevronDown, Globe } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface Language {
  code: string;
  name: string;
  flag: string;
  nativeName: string;
}

interface LanguageSelectorProps {
  className?: string;
}

const languages: Language[] = [
  {
    code: 'pt-BR',
    name: 'Português',
    flag: '🇧🇷',
    nativeName: 'Português (Brasil)',
  },
  {
    code: 'en-US',
    name: 'English',
    flag: '🇺🇸',
    nativeName: 'English (US)',
  },
];

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ className = '' }) => {
  const { i18n, t } = useTranslation();
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const currentLanguage: Language =
    languages.find((lang) => lang.code === i18n.language) || languages[0];

  const changeLanguage = async (languageCode: string): Promise<void> => {
    try {
      if (i18n && typeof i18n.changeLanguage === 'function') {
        await i18n.changeLanguage(languageCode);
      }
    } catch (error) {
      console.error('Error changing language:', error);
    }
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-300
          bg-adaptive-card border border-adaptive hover:border-adaptive-blue
          text-adaptive hover:text-adaptive-blue
        `}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        aria-label={t('common.selectLanguage', 'Selecionar idioma')}
        title={t('common.selectLanguage', 'Selecionar idioma')}
      >
        <Globe className="w-4 h-4" />
        <span className="text-sm font-medium hidden sm:inline">
          {currentLanguage.flag} {currentLanguage.name}
        </span>
        <span className="text-sm font-medium sm:hidden">{currentLanguage.flag}</span>
        <ChevronDown
          className={`w-3 h-3 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />

            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className={`
                absolute top-full mt-2 right-0 z-50
                bg-adaptive-card border border-adaptive rounded-lg shadow-lg
                min-w-[200px] overflow-hidden
              `}
            >
              <div className="py-2">
                {languages.map((language) => {
                  const isSelected = language.code === i18n.language;

                  return (
                    <motion.button
                      key={language.code}
                      onClick={() => changeLanguage(language.code)}
                      className={`
                        w-full flex items-center justify-between px-4 py-3 text-left
                        transition-all duration-200 hover:bg-adaptive-blue/10
                        ${isSelected ? 'text-adaptive-blue' : 'text-adaptive'}
                      `}
                      whileHover={{ x: 4 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{language.flag}</span>
                        <div>
                          <div className="font-medium text-sm">{language.name}</div>
                          <div className="text-xs text-adaptive-secondary">
                            {language.nativeName}
                          </div>
                        </div>
                      </div>

                      {isSelected && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{
                            type: 'spring',
                            stiffness: 500,
                            damping: 30,
                          }}
                        >
                          <Check className="w-4 h-4 text-adaptive-blue" />
                        </motion.div>
                      )}
                    </motion.button>
                  );
                })}
              </div>

              {/* Footer */}
              <div className="border-t border-adaptive px-4 py-2">
                <p className="text-xs text-adaptive-secondary">
                  {t('common.selectLanguage', 'Selecionar idioma')}
                </p>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageSelector;
