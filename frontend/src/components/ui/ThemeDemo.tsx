import { motion } from 'framer-motion';
import { Pa<PERSON>, <PERSON>, <PERSON>, <PERSON>, LucideIcon } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';

interface ThemeOption {
  id: string;
  name: string;
  icon: LucideIcon;
  description: string;
}

interface ColorPaletteItem {
  name: string;
  class: string;
  textClass: string;
}

const ThemeDemo: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  const themes: ThemeOption[] = [
    {
      id: 'system',
      name: 'Sistema',
      icon: Monitor,
      description: 'Segue a preferência do sistema',
    },
    {
      id: 'light',
      name: '<PERSON><PERSON><PERSON>',
      icon: Sun,
      description: 'Tema claro e minimalista',
    },
    {
      id: 'dark',
      name: 'Escuro',
      icon: Moon,
      description: 'Tema escuro cyberpunk',
    },
  ];

  const colorPalette: ColorPaletteItem[] = [
    { name: 'Background', class: 'bg-adaptive', textClass: 'text-adaptive' },
    { name: 'Card', class: 'bg-adaptive-card', textClass: 'text-adaptive' },
    { name: 'Border', class: 'border-adaptive border-2', textClass: 'text-adaptive' },
    { name: 'Primary Text', class: 'bg-adaptive-card', textClass: 'text-adaptive' },
    { name: 'Secondary Text', class: 'bg-adaptive-card', textClass: 'text-adaptive-secondary' },
    { name: 'Accent Blue', class: 'bg-adaptive-blue', textClass: 'text-white' },
    { name: 'Accent Green', class: 'bg-adaptive-green', textClass: 'text-white' },
    { name: 'Accent Purple', class: 'bg-adaptive-purple', textClass: 'text-white' },
  ];

  const handleThemeChange = (themeId: string): void => {
    // Para simplificar, vamos apenas usar o toggleTheme
    // Em uma implementação completa, você teria um setTheme que aceita o ID
    toggleTheme();
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center space-x-2 mb-4"
        >
          <Palette className="w-8 h-8 text-adaptive-blue" />
          <h2 className="text-3xl font-bold text-gradient">Sistema de Temas</h2>
        </motion.div>
        <p className="text-adaptive-secondary max-w-2xl mx-auto">
          Demonstração do sistema de temas adaptável com transições suaves e paleta de cores
          consistente entre light e dark mode.
        </p>
      </div>

      {/* Theme Selector */}
      <div className="card">
        <h3 className="text-xl font-semibold text-adaptive mb-4">Seletor de Tema</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {themes.map((themeOption) => {
            const Icon = themeOption.icon;
            const isActive = theme === themeOption.id;

            return (
              <motion.button
                key={themeOption.id}
                onClick={() => handleThemeChange(themeOption.id)}
                className={`
                  p-4 rounded-lg border-2 transition-all duration-300 text-left
                  ${
                    isActive
                      ? 'border-adaptive-blue bg-adaptive-blue bg-opacity-10'
                      : 'border-adaptive hover:border-adaptive-blue hover:border-opacity-50'
                  }
                `}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center space-x-3 mb-2">
                  <Icon
                    className={`w-5 h-5 ${isActive ? 'text-adaptive-blue' : 'text-adaptive-secondary'}`}
                  />
                  <span
                    className={`font-medium ${isActive ? 'text-adaptive-blue' : 'text-adaptive'}`}
                  >
                    {themeOption.name}
                  </span>
                </div>
                <p className="text-sm text-adaptive-secondary">{themeOption.description}</p>
              </motion.button>
            );
          })}
        </div>
      </div>

      {/* Color Palette */}
      <div className="card">
        <h3 className="text-xl font-semibold text-adaptive mb-4">Paleta de Cores</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {colorPalette.map((color, index) => (
            <motion.div
              key={color.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`
                ${color.class} rounded-lg p-4 min-h-[80px] flex items-center justify-center
                transition-all duration-300
              `}
            >
              <span className={`${color.textClass} text-sm font-medium text-center`}>
                {color.name}
              </span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Component Examples */}
      <div className="card">
        <h3 className="text-xl font-semibold text-adaptive mb-4">Componentes de Exemplo</h3>
        <div className="space-y-6">
          {/* Buttons */}
          <div>
            <h4 className="text-lg font-medium text-adaptive mb-3">Botões</h4>
            <div className="flex flex-wrap gap-3">
              <button className="btn-primary">Botão Primário</button>
              <button className="btn-secondary">Botão Secundário</button>
              <button className="px-4 py-2 bg-adaptive-green text-white rounded-lg hover:opacity-90 transition-opacity">
                Sucesso
              </button>
              <button className="px-4 py-2 bg-red-500 text-white rounded-lg hover:opacity-90 transition-opacity">
                Erro
              </button>
            </div>
          </div>

          {/* Form Elements */}
          <div>
            <h4 className="text-lg font-medium text-adaptive mb-3">Elementos de Formulário</h4>
            <div className="space-y-3">
              <input
                type="text"
                placeholder="Campo de texto"
                className="w-full px-4 py-2 bg-adaptive-card border border-adaptive rounded-lg text-adaptive placeholder-adaptive-secondary focus-adaptive-blue"
              />
              <textarea
                placeholder="Área de texto"
                rows={3}
                className="w-full px-4 py-2 bg-adaptive-card border border-adaptive rounded-lg text-adaptive placeholder-adaptive-secondary focus-adaptive-blue resize-none"
              />
              <select className="w-full px-4 py-2 bg-adaptive-card border border-adaptive rounded-lg text-adaptive focus-adaptive-blue">
                <option>Opção 1</option>
                <option>Opção 2</option>
                <option>Opção 3</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Theme Features */}
      <div className="card">
        <h3 className="text-xl font-semibold text-adaptive mb-4">Recursos do Sistema</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-adaptive-blue mb-2">✨ Características</h4>
            <ul className="space-y-2 text-adaptive-secondary">
              <li>• Transições suaves entre temas</li>
              <li>• Persistência da preferência</li>
              <li>• Detecção automática do sistema</li>
              <li>• Classes CSS adaptáveis</li>
              <li>• Animações com Framer Motion</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-adaptive-green mb-2">🎨 Paleta Adaptável</h4>
            <ul className="space-y-2 text-adaptive-secondary">
              <li>• Cores consistentes entre temas</li>
              <li>• Contraste otimizado</li>
              <li>• Acessibilidade garantida</li>
              <li>• Variáveis CSS customizadas</li>
              <li>• Suporte a opacidade</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeDemo;
