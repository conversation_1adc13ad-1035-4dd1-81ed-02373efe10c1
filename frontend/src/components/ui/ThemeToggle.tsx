import { motion } from 'framer-motion';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';

interface ThemeToggleProps {
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { theme, toggleTheme, isLoading } = useTheme();

  if (isLoading) {
    return (
      <div
        className={`w-12 h-6 bg-adaptive border border-adaptive rounded-full animate-pulse ${className}`}
      />
    );
  }

  return (
    <motion.button
      onClick={toggleTheme}
      className={`
        relative inline-flex items-center w-12 h-6 rounded-full transition-all duration-300 ease-in-out
        ${
          theme === 'dark'
            ? 'bg-gradient-to-r from-adaptive-purple to-adaptive-blue'
            : 'bg-gradient-to-r from-yellow-400 to-orange-500'
        }
        hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2
        ${
          theme === 'dark'
            ? 'focus:ring-adaptive-purple focus:ring-offset-adaptive'
            : 'focus:ring-yellow-400 focus:ring-offset-adaptive'
        }
        ${className}
      `}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label={`Mudar para tema ${theme === 'dark' ? 'claro' : 'escuro'}`}
      title={`Mudar para tema ${theme === 'dark' ? 'claro' : 'escuro'}`}
    >
      {/* Toggle Circle */}
      <motion.span
        className={`
          w-5 h-5 rounded-full shadow-lg flex items-center justify-center
          ${theme === 'dark' ? 'bg-adaptive' : 'bg-adaptive-card'}
        `}
        animate={{
          x: theme === 'dark' ? 2 : 24,
        }}
        transition={{
          type: 'spring',
          stiffness: 500,
          damping: 30,
        }}
      >
        {/* Icon with rotation animation */}
        <motion.div
          key={theme} // Force re-render on theme change
          initial={{ rotate: -180, opacity: 0 }}
          animate={{ rotate: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {theme === 'dark' ? (
            <Moon className="w-3 h-3 text-adaptive-purple" />
          ) : (
            <Sun className="w-3 h-3 text-yellow-600" />
          )}
        </motion.div>
      </motion.span>

      {/* Background Icons */}
      <div className="absolute inset-0 flex items-center justify-between px-1.5 pointer-events-none">
        <motion.div
          animate={{
            opacity: theme === 'dark' ? 0 : 0.6,
            scale: theme === 'dark' ? 0.8 : 1,
          }}
          transition={{ duration: 0.3 }}
        >
          <Moon className="w-3 h-3 text-white" />
        </motion.div>

        <motion.div
          animate={{
            opacity: theme === 'dark' ? 0.6 : 0,
            scale: theme === 'dark' ? 1 : 0.8,
          }}
          transition={{ duration: 0.3 }}
        >
          <Sun className="w-3 h-3 text-adaptive-card" />
        </motion.div>
      </div>
    </motion.button>
  );
};

export default ThemeToggle;
