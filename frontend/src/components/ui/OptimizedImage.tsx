import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

interface OptimizedImageProps {
  /** URL da imagem */
  src: string;
  /** Texto alternativo para acessibilidade */
  alt: string;
  /** Classes CSS adicionais */
  className?: string;
  /** Largura da imagem (px ou string CSS) */
  width?: string | number;
  /** Altura da imagem (px ou string CSS) */
  height?: string | number;
  /** Habilitar lazy loading */
  lazy?: boolean;
  /** Mostrar placeholder durante carregamento */
  placeholder?: boolean;
  /** Estilos CSS inline */
  style?: React.CSSProperties;
  /** Callback para clique na imagem */
  onClick?: () => void;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  width,
  height,
  lazy = true,
  placeholder = true,
  style,
  onClick,
}) => {
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [isInView, setIsInView] = useState<boolean>(!lazy);
  const [error, setError] = useState<boolean>(false);
  const imgRef = useRef<HTMLDivElement>(null);

  // Intersection Observer para lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  const handleLoad = (): void => {
    setIsLoaded(true);
  };

  const handleError = (): void => {
    setError(true);
    setIsLoaded(true);
  };

  // TODO: Em produção, implementar otimização de imagem
  // Por exemplo: `${CLOUDINARY_URL}/w_${width},h_${height},f_auto,q_auto/${src}`
  const optimizedSrc = src;

  // Criar style object seguro para o container
  const containerStyle: React.CSSProperties = {
    width: width,
    height: height,
    ...style, // Permite override das dimensões via style prop
  };

  return (
    <div ref={imgRef} className={`relative overflow-hidden ${className}`} style={containerStyle}>
      {/* Placeholder enquanto carrega */}
      {placeholder && !isLoaded && (
        <div className="absolute inset-0 bg-adaptive-card animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-adaptive-blue border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Imagem principal */}
      {isInView && (
        <motion.img
          src={optimizedSrc}
          alt={alt}
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={handleLoad}
          onError={handleError}
          loading={lazy ? 'lazy' : 'eager'}
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoaded ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          onClick={onClick}
        />
      )}

      {/* Fallback para erro */}
      {error && (
        <div className="absolute inset-0 bg-adaptive-card flex items-center justify-center">
          <div className="text-adaptive-secondary text-sm text-center">
            <div className="text-2xl mb-2">📷</div>
            <div>Erro ao carregar imagem</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;
