import { useEffect } from 'react';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string | string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  tags?: string[];
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  schema?: Record<string, any>;
}

const SEO: React.FC<SEOProps> = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  author = 'Blueprint Blog',
  publishedTime,
  modifiedTime,
  tags = [],
  canonical,
  noindex = false,
  nofollow = false,
  schema,
}) => {
  // Configurações padrão
  const defaultTitle = 'Blueprint Blog - Desenvolvimento e Tecnologia';
  const defaultDescription =
    'Blog pessoal sobre desenvolvimento web, tecnologia e projetos. Compartilhando conhecimento e experiências em programação.';
  const defaultImage = '/og-image.jpg';
  const defaultUrl = 'https://blueprint-blog.dev';

  const siteTitle = title ? `${title} | Blueprint Blog` : defaultTitle;
  const siteDescription = description || defaultDescription;
  const siteImage = image || defaultImage;
  const siteUrl = url || defaultUrl;
  const canonicalUrl = canonical || siteUrl;

  // Processar keywords
  const keywordsString = Array.isArray(keywords) ? keywords.join(', ') : keywords;

  // Schema.org structured data
  const defaultSchema = {
    '@context': 'https://schema.org',
    '@type': type === 'article' ? 'BlogPosting' : 'WebSite',
    name: siteTitle,
    description: siteDescription,
    url: siteUrl,
    image: siteImage,
    author: {
      '@type': 'Person',
      name: author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Blueprint Blog',
      logo: {
        '@type': 'ImageObject',
        url: `${defaultUrl}/logo.png`,
      },
    },
    ...(publishedTime && { datePublished: publishedTime }),
    ...(modifiedTime && { dateModified: modifiedTime }),
    ...(tags.length > 0 && { keywords: tags.join(', ') }),
  };

  const structuredData = schema || defaultSchema;

  useEffect(() => {
    // Atualizar título
    document.title = siteTitle;

    // Função para criar ou atualizar meta tag
    const setMetaTag = (name: string, content: string, property = false): void => {
      if (!content) return;

      const attribute = property ? 'property' : 'name';
      let meta = document.querySelector(`meta[${attribute}="${name}"]`);

      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attribute, name);
        document.head.appendChild(meta);
      }

      meta.setAttribute('content', content);
    };

    // Meta tags básicas
    setMetaTag('description', siteDescription);
    if (keywordsString) setMetaTag('keywords', keywordsString);
    setMetaTag('author', author);
    setMetaTag('robots', `${noindex ? 'noindex' : 'index'},${nofollow ? 'nofollow' : 'follow'}`);

    // Open Graph
    setMetaTag('og:type', type, true);
    setMetaTag('og:title', siteTitle, true);
    setMetaTag('og:description', siteDescription, true);
    setMetaTag('og:image', siteImage, true);
    setMetaTag('og:url', siteUrl, true);
    setMetaTag('og:site_name', 'Blueprint Blog', true);
    setMetaTag('og:locale', 'pt_BR', true);

    // Twitter Card
    setMetaTag('twitter:card', 'summary_large_image');
    setMetaTag('twitter:title', siteTitle);
    setMetaTag('twitter:description', siteDescription);
    setMetaTag('twitter:image', siteImage);
    setMetaTag('twitter:creator', '@seu_twitter');

    // Article specific
    if (type === 'article') {
      if (publishedTime) setMetaTag('article:published_time', publishedTime, true);
      if (modifiedTime) setMetaTag('article:modified_time', modifiedTime, true);
      tags.forEach((tag) => setMetaTag('article:tag', tag, true));
    }

    // Canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', canonicalUrl);

    // Structured Data
    let structuredDataScript = document.querySelector('script[type="application/ld+json"]');
    if (!structuredDataScript) {
      structuredDataScript = document.createElement('script');
      structuredDataScript.setAttribute('type', 'application/ld+json');
      document.head.appendChild(structuredDataScript);
    }
    structuredDataScript.textContent = JSON.stringify(structuredData);

    // Preconnect links
    const addPreconnect = (href: string, crossorigin = false): void => {
      if (!document.querySelector(`link[href="${href}"]`)) {
        const link = document.createElement('link');
        link.setAttribute('rel', 'preconnect');
        link.setAttribute('href', href);
        if (crossorigin) link.setAttribute('crossorigin', 'anonymous');
        document.head.appendChild(link);
      }
    };

    addPreconnect('https://fonts.googleapis.com');
    addPreconnect('https://fonts.gstatic.com', true);
  }, [
    siteTitle,
    siteDescription,
    siteImage,
    siteUrl,
    canonicalUrl,
    keywordsString,
    author,
    type,
    publishedTime,
    modifiedTime,
    tags,
    noindex,
    nofollow,
    structuredData,
  ]);

  // Este componente não renderiza nada visível
  return null;
};

export default SEO;
