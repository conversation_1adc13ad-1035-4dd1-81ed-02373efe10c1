import { useState } from 'react';
import { Co<PERSON>, Eye, Code, Shield, AlertTriangle } from 'lucide-react';
import DOMPurify from 'dompurify';

// 🎯 TypeScript interfaces
interface SvgRendererProps {
  children: React.ReactNode;
  className?: string;
  [key: string]: any; // Para ...props
}

interface SanitizedResult {
  html: string | null;
  warnings: string[] | null;
}

const SvgRenderer: React.FC<SvgRendererProps> = ({ children, className, ...props }) => {
  const [showCode, setShowCode] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);

  // Verificar se é um bloco de código SVG
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : '';

  if (language !== 'svg') {
    // Se não for SVG, renderizar como código normal
    return (
      <pre className={className} {...props}>
        <code>{children}</code>
      </pre>
    );
  }

  const svgCode = String(children).replace(/\n$/, '');

  // Função para copiar código
  const copyToClipboard = async (): Promise<void> => {
    try {
      await navigator.clipboard.writeText(svgCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Erro ao copiar:', err);
    }
  };

  // Função para validar e limpar SVG com segurança máxima
  const sanitizeSvg = (svgString: string): SanitizedResult => {
    try {
      // 1. Configuração DOMPurify para SVG
      const config = {
        USE_PROFILES: { svg: true, svgFilters: true },
        ALLOWED_TAGS: [
          'svg',
          'g',
          'path',
          'circle',
          'ellipse',
          'line',
          'rect',
          'polyline',
          'polygon',
          'text',
          'tspan',
          'defs',
          'clipPath',
          'mask',
          'pattern',
          'linearGradient',
          'radialGradient',
          'stop',
          'use',
          'symbol',
          'marker',
          'foreignObject',
        ],
        ALLOWED_ATTR: [
          'width',
          'height',
          'viewBox',
          'xmlns',
          'xmlns:xlink',
          'x',
          'y',
          'x1',
          'y1',
          'x2',
          'y2',
          'cx',
          'cy',
          'r',
          'rx',
          'ry',
          'fill',
          'stroke',
          'stroke-width',
          'stroke-linecap',
          'stroke-linejoin',
          'stroke-dasharray',
          'stroke-dashoffset',
          'opacity',
          'fill-opacity',
          'stroke-opacity',
          'transform',
          'd',
          'points',
          'class',
          'id',
          'style',
          'gradientUnits',
          'gradientTransform',
          'offset',
          'stop-color',
          'stop-opacity',
          'font-family',
          'font-size',
          'font-weight',
          'text-anchor',
          'dominant-baseline',
        ],
        FORBID_TAGS: ['script', 'object', 'embed', 'link', 'style', 'meta'],
        FORBID_ATTR: [
          'onload',
          'onerror',
          'onclick',
          'onmouseover',
          'onmouseout',
          'onmousemove',
          'onmousedown',
          'onmouseup',
          'onfocus',
          'onblur',
          'onkeydown',
          'onkeyup',
          'onkeypress',
          'onsubmit',
          'onreset',
          'onselect',
          'onchange',
          'href',
          'xlink:href',
          'src',
          'data',
        ],
        ALLOW_DATA_ATTR: false,
        ALLOW_UNKNOWN_PROTOCOLS: false,
        SANITIZE_DOM: true,
        KEEP_CONTENT: false,
      };

      // 2. Sanitizar com DOMPurify
      const cleanSvg = DOMPurify.sanitize(svgString, config);

      if (!cleanSvg || cleanSvg.trim() === '') {
        return { html: null, warnings: ['SVG vazio após sanitização'] };
      }

      // 3. Parse adicional para validação
      const parser = new DOMParser();
      const doc = parser.parseFromString(cleanSvg, 'image/svg+xml');

      // Verificar erros de parsing
      const errorNode = doc.querySelector('parsererror');
      if (errorNode) {
        return { html: null, warnings: ['SVG malformado'] };
      }

      // 4. Obter elemento SVG
      const svgElement = doc.querySelector('svg');
      if (!svgElement) {
        return { html: null, warnings: ['Elemento SVG não encontrado'] };
      }

      // 5. Validações de segurança adicionais
      const warnings = [];

      // Verificar se tem dimensões
      if (
        !svgElement.getAttribute('viewBox') &&
        !svgElement.getAttribute('width') &&
        !svgElement.getAttribute('height')
      ) {
        svgElement.setAttribute('viewBox', '0 0 100 100');
        warnings.push('Dimensões padrão aplicadas');
      }

      // Limitar tamanho máximo
      const width = svgElement.getAttribute('width');
      const height = svgElement.getAttribute('height');
      if (width && parseInt(width) > 1000) {
        svgElement.setAttribute('width', '1000');
        warnings.push('Largura limitada a 1000px');
      }
      if (height && parseInt(height) > 1000) {
        svgElement.setAttribute('height', '1000');
        warnings.push('Altura limitada a 1000px');
      }

      // Adicionar classes de segurança
      svgElement.setAttribute('class', 'max-w-full h-auto svg-safe');

      // Remover qualquer xmlns malicioso
      svgElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg');

      return {
        html: svgElement.outerHTML,
        warnings: warnings.length > 0 ? warnings : null,
      };
    } catch (error) {
      console.error('Erro ao processar SVG:', error);
      return { html: null, warnings: ['Erro interno de processamento'] };
    }
  };

  const sanitizedResult = sanitizeSvg(svgCode);
  const { html: sanitizedSvg, warnings } = sanitizedResult;

  return (
    <div className="my-6 border border-dark-border rounded-lg overflow-hidden bg-dark-card">
      {/* Header com controles */}
      <div className="flex items-center justify-between px-4 py-2 bg-dark-bg border-b border-dark-border">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span className="ml-2 text-sm text-dark-text-secondary">SVG</span>
          {sanitizedSvg && <Shield className="w-4 h-4 text-green-400" />}
          {warnings && <AlertTriangle className="w-4 h-4 text-yellow-400" />}
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowCode(!showCode)}
            className="p-1 rounded hover:bg-dark-border transition-colors"
            title={showCode ? 'Ver imagem' : 'Ver código'}
          >
            {showCode ? <Eye className="w-4 h-4" /> : <Code className="w-4 h-4" />}
          </button>

          <button
            onClick={copyToClipboard}
            className="p-1 rounded hover:bg-dark-border transition-colors"
            title="Copiar código"
          >
            <Copy className="w-4 h-4" />
          </button>

          {copied && <span className="text-xs text-green-400">Copiado!</span>}
        </div>
      </div>

      {/* Avisos de segurança */}
      {warnings && (
        <div className="px-4 py-2 bg-yellow-500/10 border-b border-yellow-500/20">
          <div className="flex items-center gap-2 text-yellow-400 text-sm">
            <AlertTriangle className="w-4 h-4" />
            <span>Modificações de segurança aplicadas:</span>
          </div>
          <ul className="mt-1 text-xs text-yellow-300 ml-6">
            {warnings.map((warning, index) => (
              <li key={index}>• {warning}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Conteúdo */}
      <div className="p-4">
        {showCode ? (
          /* Mostrar código */
          <pre className="bg-dark-bg rounded-lg p-4 overflow-x-auto">
            <code className="text-sm text-dark-text font-mono">{svgCode}</code>
          </pre>
        ) : (
          /* Mostrar SVG renderizado */
          <div className="flex justify-center items-center min-h-[100px] bg-white/5 rounded-lg p-4">
            {sanitizedSvg ? (
              <div dangerouslySetInnerHTML={{ __html: sanitizedSvg }} className="max-w-full" />
            ) : (
              <div className="text-center text-dark-text-secondary">
                <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-red-400" />
                <p className="text-sm font-medium text-red-400">SVG Bloqueado por Segurança</p>
                <p className="text-xs mt-1">Este SVG contém elementos potencialmente perigosos</p>
                <button
                  onClick={() => setShowCode(true)}
                  className="text-xs text-neon-blue hover:text-blue-400 mt-2"
                >
                  Ver código original
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer com informações */}
      {!showCode && sanitizedSvg && (
        <div className="px-4 py-2 bg-dark-bg border-t border-dark-border">
          <div className="flex items-center justify-between">
            <p className="text-xs text-dark-text-secondary">
              💡 Clique no ícone de código para ver o SVG source
            </p>
            <div className="flex items-center gap-1 text-xs text-green-400">
              <Shield className="w-3 h-3" />
              <span>Seguro</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SvgRenderer;
