import { Copy, ExternalLink } from 'lucide-react';
import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import type { Components } from 'react-markdown';
// 🚀 Importação otimizada - apenas o que precisamos
import { Light as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomOneDark } from 'react-syntax-highlighter/dist/esm/styles/hljs';
// 🎯 Registrar apenas linguagens essenciais
import javascript from 'react-syntax-highlighter/dist/esm/languages/hljs/javascript';
import typescript from 'react-syntax-highlighter/dist/esm/languages/hljs/typescript';
import python from 'react-syntax-highlighter/dist/esm/languages/hljs/python';
import rust from 'react-syntax-highlighter/dist/esm/languages/hljs/rust';
import bash from 'react-syntax-highlighter/dist/esm/languages/hljs/bash';
import json from 'react-syntax-highlighter/dist/esm/languages/hljs/json';
import css from 'react-syntax-highlighter/dist/esm/languages/hljs/css';
import html from 'react-syntax-highlighter/dist/esm/languages/hljs/xml';
import remarkFrontmatter from 'remark-frontmatter';
import remarkGfm from 'remark-gfm';
import SvgRenderer from './SvgRenderer';

// 🔧 Registrar linguagens
SyntaxHighlighter.registerLanguage('javascript', javascript);
SyntaxHighlighter.registerLanguage('js', javascript);
SyntaxHighlighter.registerLanguage('typescript', typescript);
SyntaxHighlighter.registerLanguage('ts', typescript);
SyntaxHighlighter.registerLanguage('tsx', typescript);
SyntaxHighlighter.registerLanguage('jsx', javascript);
SyntaxHighlighter.registerLanguage('python', python);
SyntaxHighlighter.registerLanguage('py', python);
SyntaxHighlighter.registerLanguage('rust', rust);
SyntaxHighlighter.registerLanguage('rs', rust);
SyntaxHighlighter.registerLanguage('bash', bash);
SyntaxHighlighter.registerLanguage('sh', bash);
SyntaxHighlighter.registerLanguage('shell', bash);
SyntaxHighlighter.registerLanguage('json', json);
SyntaxHighlighter.registerLanguage('css', css);
SyntaxHighlighter.registerLanguage('html', html);
SyntaxHighlighter.registerLanguage('xml', html);

// 🎯 TypeScript interfaces
interface MarkdownRendererProps {
  content: string;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className = '' }) => {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const copyToClipboard = async (text: string, id: string): Promise<void> => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCode(id);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const components: Components = {
    // Headings
    h1: ({ children, ...props }) => (
      <h1 className="text-3xl font-bold text-gradient mb-6 mt-8 first:mt-0" {...props}>
        {children}
      </h1>
    ),
    h2: ({ children, ...props }) => (
      <h2 className="text-2xl font-semibold text-gradient mb-4 mt-6" {...props}>
        {children}
      </h2>
    ),
    h3: ({ children, ...props }) => (
      <h3 className="text-xl font-semibold text-adaptive mb-3 mt-4" {...props}>
        {children}
      </h3>
    ),
    h4: ({ children, ...props }) => (
      <h4 className="text-lg font-semibold text-adaptive mb-2 mt-3" {...props}>
        {children}
      </h4>
    ),

    // Paragraphs
    p: ({ children, ...props }) => (
      <p className="text-adaptive-secondary leading-relaxed mb-4" {...props}>
        {children}
      </p>
    ),

    // Links
    a: ({ href, children, ...props }) => {
      const isExternal = href?.startsWith('http');
      return (
        <a
          href={href}
          target={isExternal ? '_blank' : undefined}
          rel={isExternal ? 'noopener noreferrer' : undefined}
          className="text-adaptive-blue hover:text-adaptive-green transition-colors inline-flex items-center gap-1"
          {...props}
        >
          {children}
          {isExternal && <ExternalLink className="h-3 w-3" />}
        </a>
      );
    },

    // Lists
    ul: ({ children, ...props }) => (
      <ul className="list-disc list-inside text-adaptive-secondary mb-4 space-y-1" {...props}>
        {children}
      </ul>
    ),
    ol: ({ children, ...props }) => (
      <ol className="list-decimal list-inside text-adaptive-secondary mb-4 space-y-1" {...props}>
        {children}
      </ol>
    ),
    li: ({ children, ...props }) => (
      <li className="ml-4" {...props}>
        {children}
      </li>
    ),

    // Blockquote
    blockquote: ({ children, ...props }) => (
      <blockquote
        className="border-l-4 border-adaptive-blue pl-4 py-2 my-6 bg-adaptive-blue/5 italic text-adaptive"
        {...props}
      >
        {children}
      </blockquote>
    ),

    // Code blocks
    code: ({ node, className, children, ...props }: any) => {
      const inline = !className;
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';
      const codeId = `code-${Math.random().toString(36).substr(2, 9)}`;

      if (!inline && match) {
        // Se for SVG, usar o SvgRenderer
        if (language === 'svg') {
          return (
            <SvgRenderer className={className} {...props}>
              {children}
            </SvgRenderer>
          );
        }

        // Para outras linguagens, usar SyntaxHighlighter normal
        return (
          <div className="relative group my-6">
            <div className="flex items-center justify-between bg-adaptive-card border border-adaptive rounded-t-lg px-4 py-2">
              <span className="text-adaptive-secondary text-sm font-medium">{language}</span>
              <button
                onClick={() => copyToClipboard(String(children).replace(/\n$/, ''), codeId)}
                className="flex items-center gap-2 text-adaptive-secondary hover:text-adaptive-blue transition-colors text-sm"
                title="Copy code"
              >
                <Copy className="h-4 w-4" />
                {copiedCode === codeId ? 'Copied!' : 'Copy'}
              </button>
            </div>
            <SyntaxHighlighter
              style={atomOneDark as any}
              language={language}
              PreTag="div"
              className="!mt-0 !rounded-t-none border border-t-0 border-adaptive"
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          </div>
        );
      }

      return (
        <code
          className="bg-adaptive-card text-adaptive px-1.5 py-0.5 rounded text-sm border border-adaptive"
          {...props}
        >
          {children}
        </code>
      );
    },

    // Images
    img: ({ src, alt, ...props }) => (
      <div className="my-6">
        <img
          src={src}
          alt={alt}
          className="w-full rounded-lg border border-adaptive"
          loading="lazy"
          {...props}
        />
        {alt && <p className="text-center text-sm text-adaptive-secondary mt-2 italic">{alt}</p>}
      </div>
    ),

    // Tables
    table: ({ children, ...props }) => (
      <div className="overflow-x-auto my-6">
        <table className="w-full border-collapse border border-adaptive" {...props}>
          {children}
        </table>
      </div>
    ),
    thead: ({ children, ...props }) => (
      <thead className="bg-adaptive-card" {...props}>
        {children}
      </thead>
    ),
    th: ({ children, ...props }) => (
      <th
        className="border border-adaptive px-4 py-2 text-left font-semibold text-adaptive"
        {...props}
      >
        {children}
      </th>
    ),
    td: ({ children, ...props }) => (
      <td className="border border-adaptive px-4 py-2 text-adaptive-secondary" {...props}>
        {children}
      </td>
    ),

    // Horizontal rule
    hr: ({ ...props }) => <hr className="border-adaptive my-8" {...props} />,

    // Strong and emphasis
    strong: ({ children, ...props }) => (
      <strong className="font-semibold text-adaptive-blue" {...props}>
        {children}
      </strong>
    ),
    em: ({ children, ...props }) => (
      <em className="italic text-adaptive-green" {...props}>
        {children}
      </em>
    ),
  };

  return (
    <div className={`prose prose-invert max-w-none ${className}`}>
      <ReactMarkdown remarkPlugins={[remarkGfm, remarkFrontmatter]} components={components}>
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
