import clsx from 'clsx';
import { motion } from 'framer-motion';
import { ArrowRight, Calendar, Clock, FileText, Star, User } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Post } from '../../types';

interface Category {
  name: string;
  category?: {
    name: string;
  };
}

interface PostCardProps {
  post: Post & {
    reading_time?: number;
    tags?: string[];
    categories?: Category[];
    featured_image?: string;
    author_name?: string;
  };
  featured?: boolean;
  compact?: boolean;
  className?: string;
}

function PostCard({ post, featured = false, compact = false, className = '' }: PostCardProps) {
  console.log('🃏 [PostCard] Renderizando post:', {
    slug: post?.slug,
    title: post?.title,
    featured,
    compact,
    postKeys: post ? Object.keys(post) : 'post é null',
  });

  // Adaptar para estrutura do Supabase
  const {
    slug,
    title,
    excerpt,
    reading_time,
    tags = [],
    categories = [],
    created_at,
    published_at,
    featured_image,
    status,
    author_name,
  } = post;

  // Nome do autor vem diretamente do campo author_name
  const authorName = author_name || 'Autor Desconhecido';

  // Formatação de data com validação
  const formatDate = (dateString: string | null): string | null => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return null;
      return date.toLocaleDateString('pt-BR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return null;
    }
  };

  const formattedDate = formatDate(published_at) || formatDate(created_at);

  // Formatação do tempo de leitura
  const readingTime = reading_time ? `${reading_time} min de leitura` : null;

  // Extrair nomes das categorias (com verificação de null)
  const categoryNames = (categories || [])
    .map((cat: Category) => cat.category?.name || cat.name)
    .filter(Boolean);

  // Verificar se é featured
  const isFeatured = status === 'published' && featured;

  const cardContent = (
    <article
      className={clsx(
        'card group hover:scale-105 transition-all duration-300',
        featured &&
          'border-adaptive-blue/50 bg-gradient-to-br from-adaptive-card to-adaptive-card/50',
        compact && 'p-4', // Padding menor para compact
        className
      )}
    >
      {/* Featured Image Thumbnail */}
      <div
        className={clsx(
          'mb-4 overflow-hidden rounded-lg border border-adaptive',
          compact && 'mb-3'
        )}
      >
        {featured_image ? (
          <img
            src={featured_image}
            alt={title}
            className={clsx(
              'w-full object-cover group-hover:scale-105 transition-transform duration-300',
              compact ? 'h-40' : 'h-56'
            )}
            loading="lazy"
            onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
              e.currentTarget.style.display = 'none';
            }}
          />
        ) : (
          <div
            className={clsx(
              'w-full bg-gradient-to-br from-adaptive-blue/10 to-adaptive-purple/10 flex items-center justify-center',
              compact ? 'h-40' : 'h-56'
            )}
          >
            <FileText
              className={clsx('text-adaptive-blue/50', compact ? 'h-10 w-10' : 'h-16 w-16')}
            />
          </div>
        )}
      </div>

      {/* Featured Badge */}
      {(featured || isFeatured) && !compact && (
        <div className="absolute top-2 right-2 bg-adaptive-blue text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1 z-10">
          <Star className="h-3 w-3" />
          Destaque
        </div>
      )}

      {/* Categories */}
      {categoryNames.length > 0 && !compact && (
        <div className="flex flex-wrap gap-2 mb-3">
          {categoryNames.slice(0, 2).map((category: string, index: number) => (
            <span
              key={index}
              className="bg-adaptive-blue/10 text-adaptive-blue px-2 py-1 rounded text-xs font-medium border border-adaptive-blue/20"
            >
              {category}
            </span>
          ))}
          {categoryNames.length > 2 && (
            <span className="text-adaptive-secondary text-xs">
              +{categoryNames.length - 2} mais
            </span>
          )}
        </div>
      )}

      {/* Title */}
      <h3
        className={clsx(
          'font-semibold text-gradient mb-3 group-hover:text-adaptive-blue transition-colors',
          compact ? 'text-base' : 'text-xl'
        )}
      >
        <Link to={`/blog/${slug}`} className="hover:underline">
          {title}
        </Link>
      </h3>

      {/* Excerpt - Oculto em compact */}
      {!compact && (
        <p className="text-adaptive-secondary leading-relaxed mb-4 line-clamp-3">{excerpt}</p>
      )}

      {/* Meta Information */}
      <div
        className={clsx(
          'flex flex-wrap items-center gap-4 text-adaptive-secondary mb-4',
          compact ? 'text-xs' : 'text-sm'
        )}
      >
        {/* Author */}
        <div className="flex items-center gap-1">
          <User className="h-3 w-3" />
          <span>{authorName}</span>
        </div>

        {formattedDate && (
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{formattedDate}</span>
          </div>
        )}

        {readingTime && (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{readingTime}</span>
          </div>
        )}
      </div>

      {/* Tags - Só mostra em compact ou se não há excerpt */}
      {(compact || !excerpt) && (tags || []).length > 0 && (
        <div className="flex flex-wrap gap-1 mb-4">
          {(tags || []).slice(0, compact ? 2 : 3).map((tag: string) => (
            <span
              key={tag}
              className="bg-adaptive text-adaptive-secondary px-2 py-1 rounded text-xs border border-adaptive"
            >
              #{tag}
            </span>
          ))}
          {(tags || []).length > (compact ? 2 : 3) && (
            <span className="text-adaptive-secondary text-xs">
              +{(tags || []).length - (compact ? 2 : 3)}
            </span>
          )}
        </div>
      )}

      {/* Read More Link */}
      <Link
        to={`/blog/${slug}`}
        className={clsx(
          'inline-flex items-center text-adaptive-blue hover:text-adaptive-green transition-colors font-medium group',
          compact ? 'text-xs' : 'text-sm'
        )}
      >
        {compact ? 'Ler' : 'Ler artigo'}
        <ArrowRight className="ml-1 h-3 w-3 group-hover:translate-x-1 transition-transform" />
      </Link>
    </article>
  );

  // Se é compact, não usa motion para evitar animação de entrada
  if (compact) {
    return cardContent;
  }

  // Versão completa com motion
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {cardContent}
    </motion.div>
  );
}

export default PostCard;
