import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Plus, Edit, Trash2, Eye, Calendar, Tag, Search } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { usePosts } from '../../hooks/usePosts';
import { apiService } from '../../services/api';

// 🎯 TypeScript interfaces
interface User {
  id: string;
  email: string;
  name?: string;
}

interface Post {
  id: string;
  title: string;
  content: string;
  slug: string;
  status: 'draft' | 'published' | 'archived';
  language: 'pt' | 'en';
  updated_at: string;
  published_at?: string | null;
  views?: number;
  tags?: string[];
}

type StatusFilter = 'all' | 'draft' | 'published' | 'archived';
type LanguageFilter = 'all' | 'pt' | 'en';

const PostsList: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [languageFilter, setLanguageFilter] = useState<LanguageFilter>('all');

  const { posts, loading, error, deletePost } = usePosts({
    status: statusFilter === 'all' ? undefined : statusFilter,
    language: languageFilter === 'all' ? undefined : languageFilter,
    orderBy: 'updated_at',
    orderDirection: 'desc',
  });

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async (): Promise<void> => {
    const response = await apiService.getCurrentUser();
    const currentUser = response.data;
    if (!currentUser) {
      navigate('/login');
      return;
    }
    setUser(currentUser);
  };

  const filteredPosts = posts.filter(
    (post) =>
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (post.tags && post.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase())))
  );

  const handleDelete = async (post: any): Promise<void> => {
    if (window.confirm(`Tem certeza que deseja deletar o post "${post.title}"?`)) {
      const { error } = await deletePost(post.id);
      if (error) {
        alert(`Erro ao deletar: ${error}`);
      } else {
        alert('Post deletado com sucesso!');
      }
    }
  };

  const getStatusBadge = (status: Post['status']): React.ReactElement => {
    const styles = {
      draft: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      published: 'bg-green-500/20 text-green-400 border-green-500/30',
      archived: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
    };

    const labels = {
      draft: 'Rascunho',
      published: 'Publicado',
      archived: 'Arquivado',
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs border ${styles[status]}`}>
        {labels[status]}
      </span>
    );
  };

  const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return '-';
    return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: ptBR });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-dark-bg flex items-center justify-center">
        <div className="text-neon-blue">Carregando posts...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-bg text-dark-text">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Gerenciar Posts</h1>
            <p className="text-dark-text-secondary">{filteredPosts.length} post(s) encontrado(s)</p>
          </div>
          <Link
            to="/admin/posts/new"
            className="flex items-center gap-2 px-4 py-2 rounded-lg bg-neon-blue text-dark-bg hover:bg-blue-600 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Novo Post
          </Link>
        </div>

        {/* Filtros */}
        <div className="bg-dark-card rounded-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Busca */}
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-dark-text-secondary" />
                <input
                  type="text"
                  placeholder="Buscar posts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-lg bg-dark-bg border border-dark-border focus:border-neon-blue focus:outline-none"
                />
              </div>
            </div>

            {/* Filtro Status */}
            <div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as StatusFilter)}
                className="w-full px-3 py-2 rounded-lg bg-dark-bg border border-dark-border focus:border-neon-blue focus:outline-none"
              >
                <option value="all">Todos os status</option>
                <option value="draft">Rascunhos</option>
                <option value="published">Publicados</option>
                <option value="archived">Arquivados</option>
              </select>
            </div>

            {/* Filtro Idioma */}
            <div>
              <select
                value={languageFilter}
                onChange={(e) => setLanguageFilter(e.target.value as LanguageFilter)}
                className="w-full px-3 py-2 rounded-lg bg-dark-bg border border-dark-border focus:border-neon-blue focus:outline-none"
              >
                <option value="all">Todos os idiomas</option>
                <option value="pt">Português</option>
                <option value="en">English</option>
              </select>
            </div>
          </div>
        </div>

        {/* Lista de Posts */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/30 text-red-400 rounded-lg p-4 mb-6">
            Erro ao carregar posts: {error}
          </div>
        )}

        {filteredPosts.length === 0 ? (
          <div className="bg-dark-card rounded-lg p-12 text-center">
            <div className="text-dark-text-secondary mb-4">
              {searchTerm
                ? 'Nenhum post encontrado com os filtros aplicados.'
                : 'Nenhum post encontrado.'}
            </div>
            <Link
              to="/admin/posts/new"
              className="inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-neon-blue text-dark-bg hover:bg-blue-600 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Criar Primeiro Post
            </Link>
          </div>
        ) : (
          <div className="bg-dark-card rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-dark-bg">
                  <tr>
                    <th className="text-left px-6 py-4 font-medium">Título</th>
                    <th className="text-left px-6 py-4 font-medium">Status</th>
                    <th className="text-left px-6 py-4 font-medium">Idioma</th>
                    <th className="text-left px-6 py-4 font-medium">Atualizado</th>
                    <th className="text-left px-6 py-4 font-medium">Views</th>
                    <th className="text-right px-6 py-4 font-medium">Ações</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-dark-border">
                  {filteredPosts.map((post) => (
                    <tr key={post.id} className="hover:bg-dark-bg/50 transition-colors">
                      <td className="px-6 py-4">
                        <div>
                          <div className="font-medium mb-1">{post.title}</div>
                          <div className="text-sm text-dark-text-secondary">/{post.slug}</div>
                          {post.tags && post.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {post.tags.slice(0, 3).map((tag, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-dark-bg text-xs"
                                >
                                  <Tag className="w-3 h-3" />
                                  {tag}
                                </span>
                              ))}
                              {post.tags.length > 3 && (
                                <span className="text-xs text-dark-text-secondary">
                                  +{post.tags.length - 3} mais
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">{getStatusBadge(post.status)}</td>
                      <td className="px-6 py-4">
                        <span className="text-sm">
                          {post.language === 'pt' ? '🇧🇷 PT' : '🇺🇸 EN'}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm">
                          <div>{formatDate(post.updated_at)}</div>
                          {post.published_at && (
                            <div className="text-dark-text-secondary text-xs">
                              Pub: {formatDate(post.published_at)}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-1 text-sm">
                          <Eye className="w-4 h-4" />
                          {post.views || 0}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center justify-end gap-2">
                          {post.status === 'published' && (
                            <Link
                              to={`/blog/${post.slug}`}
                              className="p-2 rounded-lg bg-dark-bg hover:bg-dark-border transition-colors"
                              title="Ver post"
                            >
                              <Eye className="w-4 h-4" />
                            </Link>
                          )}
                          <Link
                            to={`/admin/posts/edit/${post.slug}`}
                            className="p-2 rounded-lg bg-dark-bg hover:bg-dark-border transition-colors"
                            title="Editar"
                          >
                            <Edit className="w-4 h-4" />
                          </Link>
                          <button
                            onClick={() => handleDelete(post)}
                            className="p-2 rounded-lg bg-dark-bg hover:bg-red-500/20 text-red-400 transition-colors"
                            title="Deletar"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PostsList;
