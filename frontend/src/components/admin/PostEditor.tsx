import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import toast from 'react-hot-toast';
import { Save, Eye, EyeOff, ArrowLeft, Calendar, Tag, Image } from 'lucide-react';
import MarkdownRenderer from '../blog/MarkdownRenderer';
import { usePosts } from '../../hooks/usePosts';
import { apiService } from '../../services/api';

// 🎯 TypeScript interfaces
interface User {
  id: string;
  email: string;
  name?: string;
}

interface PostFormData {
  id?: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image: string;
  status: 'draft' | 'published' | 'archived';
  language: 'pt' | 'en';
  tags: string[];
  published_at: string | null;
  created_at?: string;
  author_id?: string;
  reading_time?: number;
}

const PostEditor: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { slug } = useParams<{ slug?: string }>();
  const { createPost, updatePost, getPostBySlug } = usePosts();

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [formData, setFormData] = useState<PostFormData>({
    title: '',
    slug: '',
    content: '',
    excerpt: '',
    featured_image: '',
    status: 'draft',
    language: i18n.language === 'pt-BR' ? 'pt' : 'en', // Garantir valor válido
    tags: [],
    published_at: null,
  });

  const [tagInput, setTagInput] = useState<string>('');

  useEffect(() => {
    checkAuth();
    if (slug) {
      loadPost();
    }
  }, [slug]);

  const checkAuth = async (): Promise<void> => {
    const response = await apiService.getCurrentUser();
    const currentUser = response.data;
    if (!currentUser) {
      navigate('/login');
      return;
    }
    setUser(currentUser);
  };

  const loadPost = async (): Promise<void> => {
    if (!slug) return;

    setLoading(true);
    const { data, error } = await getPostBySlug(slug);

    if (error) {
      console.error('Erro ao carregar post:', error);
      toast.error('Erro ao carregar post');
      navigate('/admin/posts');
      return;
    }

    if (data) {
      setFormData({
        title: data.title || '',
        slug: data.slug || '',
        content: data.content || '',
        excerpt: data.excerpt || '',
        featured_image: (data as any).featured_image || '',
        status: data.status || 'draft',
        language: data.language || 'pt',
        tags: data.tags || [],
        published_at: data.published_at || null,
      });
    }
    setLoading(false);
  };

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-+|-+$/g, ''); // Remove hífens do início e fim
  };

  const handleInputChange = (field: keyof PostFormData, value: any): void => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
      ...(field === 'title' && !slug ? { slug: generateSlug(value) } : {}),
    }));
  };

  const handleAddTag = (): void => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string): void => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const calculateReadingTime = (content: string): number => {
    const wordsPerMinute = 200;
    const words = content.trim().split(/\s+/).length;
    return Math.ceil(words / wordsPerMinute);
  };

  const handleSave = async (status: PostFormData['status'] = formData.status): Promise<void> => {
    if (!formData.title.trim() || !formData.content.trim()) {
      toast.error('Título e conteúdo são obrigatórios!');
      return;
    }

    setLoading(true);

    const postData = {
      ...formData,
      status,
      reading_time: calculateReadingTime(formData.content),
      published_at:
        status === 'published'
          ? formData.published_at || new Date().toISOString()
          : status === 'draft'
            ? null
            : formData.published_at,
    };

    let result;
    if (slug && formData.id) {
      // Remover campos que não devem ser atualizados
      const { id, created_at, author_id, ...updateData } = postData;
      result = await updatePost(formData.id, updateData);
    } else {
      result = await createPost(postData);
    }

    if (result.error) {
      toast.error(`Erro ao salvar: ${result.error}`);
    } else {
      const action = status === 'published' ? 'publicado' : 'salvo';
      toast.success(`Post ${action} com sucesso!`);
      if (!slug && result.data?.slug) {
        navigate(`/admin/posts/edit/${result.data.slug}`);
      }
    }

    setLoading(false);
  };

  const handlePublish = (): void => {
    const action = slug ? 'salvar' : 'publicar';
    const message = slug
      ? 'Tem certeza que deseja salvar as alterações?'
      : 'Tem certeza que deseja publicar este post?';

    if (window.confirm(message)) {
      handleSave('published');
    }
  };

  if (loading && slug) {
    return (
      <div className="min-h-screen bg-dark-bg flex items-center justify-center">
        <div className="text-neon-blue">Carregando...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-bg text-dark-text">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('/admin/posts')}
              className="p-2 rounded-lg bg-dark-card hover:bg-dark-border transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-2xl font-bold">{slug ? 'Editar Post' : 'Novo Post'}</h1>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center gap-2 px-4 py-2 rounded-lg bg-dark-card hover:bg-dark-border transition-colors"
            >
              {showPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showPreview ? 'Editor' : 'Preview'}
            </button>

            <button
              onClick={() => handleSave('draft')}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 rounded-lg bg-dark-card hover:bg-dark-border transition-colors disabled:opacity-50"
            >
              <Save className="w-4 h-4" />
              Salvar Rascunho
            </button>

            <button
              onClick={handlePublish}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 rounded-lg bg-neon-blue text-dark-bg hover:bg-blue-600 transition-colors disabled:opacity-50"
            >
              {slug ? 'Salvar' : 'Publicar'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Editor */}
          <div className={`${showPreview ? 'lg:col-span-1' : 'lg:col-span-2'} space-y-6`}>
            {/* Título */}
            <div>
              <label className="block text-sm font-medium mb-2">Título</label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 rounded-lg bg-dark-card border border-dark-border focus:border-neon-blue focus:outline-none"
                placeholder="Digite o título do post..."
              />
            </div>

            {/* Slug */}
            <div>
              <label className="block text-sm font-medium mb-2">Slug (URL)</label>
              <input
                type="text"
                value={formData.slug}
                onChange={(e) => handleInputChange('slug', e.target.value)}
                className="w-full px-4 py-3 rounded-lg bg-dark-card border border-dark-border focus:border-neon-blue focus:outline-none"
                placeholder="slug-do-post"
              />
            </div>

            {/* Conteúdo */}
            <div>
              <label className="block text-sm font-medium mb-2">Conteúdo (Markdown)</label>
              <textarea
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                rows={20}
                className="w-full px-4 py-3 rounded-lg bg-dark-card border border-dark-border focus:border-neon-blue focus:outline-none font-mono text-sm"
                placeholder="Digite o conteúdo em Markdown..."
              />
            </div>
          </div>

          {/* Sidebar / Preview */}
          <div className="lg:col-span-1 space-y-6">
            {showPreview ? (
              /* Preview */
              <div className="bg-dark-card rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Preview</h3>
                <div className="prose prose-invert max-w-none">
                  {/* Título */}
                  <h1 className="text-3xl font-bold mb-4">{formData.title || 'Título do Post'}</h1>

                  {/* Imagem destacada */}
                  {formData.featured_image && (
                    <div className="mb-6">
                      <img
                        src={formData.featured_image}
                        alt={formData.title || 'Imagem destacada'}
                        className="w-full h-64 object-cover rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          const nextElement = target.nextElementSibling as HTMLElement;
                          target.style.display = 'none';
                          if (nextElement) nextElement.style.display = 'block';
                        }}
                      />
                      <div className="hidden w-full h-64 bg-dark-bg border-2 border-dashed border-dark-border rounded-lg flex items-center justify-center text-dark-text-secondary">
                        ❌ Erro ao carregar imagem
                      </div>
                    </div>
                  )}

                  {/* Resumo */}
                  {formData.excerpt && (
                    <div className="mb-6 p-4 bg-dark-bg rounded-lg border-l-4 border-neon-blue">
                      <p className="text-dark-text-secondary italic">{formData.excerpt}</p>
                    </div>
                  )}

                  {/* Tags */}
                  {formData.tags && formData.tags.length > 0 && (
                    <div className="mb-6 flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-neon-blue/20 text-neon-blue text-sm"
                        >
                          <Tag className="w-3 h-3" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}

                  {/* Conteúdo */}
                  <div className="prose prose-invert max-w-none">
                    <MarkdownRenderer
                      content={formData.content || '*Escreva seu conteúdo aqui...*'}
                    />
                  </div>
                </div>
              </div>
            ) : (
              /* Configurações */
              <>
                {/* Excerpt */}
                <div className="bg-dark-card rounded-lg p-6">
                  <label className="block text-sm font-medium mb-2">Resumo</label>
                  <textarea
                    value={formData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 rounded-lg bg-dark-bg border border-dark-border focus:border-neon-blue focus:outline-none text-sm"
                    placeholder="Breve descrição do post..."
                  />
                </div>

                {/* Imagem destacada */}
                <div className="bg-dark-card rounded-lg p-6">
                  <label className="block text-sm font-medium mb-2 flex items-center gap-2">
                    <Image className="w-4 h-4" />
                    Imagem Destacada
                  </label>
                  <input
                    type="url"
                    value={formData.featured_image}
                    onChange={(e) => handleInputChange('featured_image', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg bg-dark-bg border border-dark-border focus:border-neon-blue focus:outline-none text-sm mb-3"
                    placeholder="https://exemplo.com/imagem.jpg"
                  />
                  {/* Preview da imagem */}
                  {formData.featured_image && (
                    <div className="mt-3">
                      <img
                        src={formData.featured_image}
                        alt="Preview"
                        className="w-full h-32 object-cover rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          const nextElement = target.nextElementSibling as HTMLElement;
                          target.style.display = 'none';
                          if (nextElement) nextElement.style.display = 'block';
                        }}
                      />
                      <div className="hidden w-full h-32 bg-dark-bg border-2 border-dashed border-red-500/30 rounded-lg flex items-center justify-center text-red-400 text-sm">
                        ❌ URL inválida
                      </div>
                    </div>
                  )}
                </div>

                {/* Tags */}
                <div className="bg-dark-card rounded-lg p-6">
                  <label className="block text-sm font-medium mb-2 flex items-center gap-2">
                    <Tag className="w-4 h-4" />
                    Tags
                  </label>
                  <div className="flex gap-2 mb-3">
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                      className="flex-1 px-3 py-2 rounded-lg bg-dark-bg border border-dark-border focus:border-neon-blue focus:outline-none text-sm"
                      placeholder="Adicionar tag..."
                    />
                    <button
                      onClick={handleAddTag}
                      className="px-3 py-2 rounded-lg bg-neon-blue text-dark-bg hover:bg-blue-600 transition-colors text-sm"
                    >
                      +
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-dark-bg text-xs"
                      >
                        {tag}
                        <button
                          onClick={() => handleRemoveTag(tag)}
                          className="text-red-400 hover:text-red-300"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>

                {/* Idioma */}
                <div className="bg-dark-card rounded-lg p-6">
                  <label className="block text-sm font-medium mb-2">Idioma</label>
                  <select
                    value={formData.language}
                    onChange={(e) => handleInputChange('language', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg bg-dark-bg border border-dark-border focus:border-neon-blue focus:outline-none text-sm"
                  >
                    <option value="pt">🇧🇷 Português</option>
                    <option value="en">🇺🇸 English</option>
                  </select>
                </div>

                {/* Status */}
                <div className="bg-dark-card rounded-lg p-6">
                  <label className="block text-sm font-medium mb-2">Status</label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg bg-dark-bg border border-dark-border focus:border-neon-blue focus:outline-none text-sm"
                  >
                    <option value="draft">Rascunho</option>
                    <option value="published">Publicado</option>
                    <option value="archived">Arquivado</option>
                  </select>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostEditor;
