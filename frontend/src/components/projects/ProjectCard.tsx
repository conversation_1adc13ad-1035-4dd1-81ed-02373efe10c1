import clsx from 'clsx';
import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Clock, ExternalLink, Github, Star } from 'lucide-react';
import { Project } from '../../types';

interface ProjectCardProps {
  project: Project;
  featured?: boolean;
  compact?: boolean;
  className?: string;
}

function ProjectCard({
  project,
  featured = false,
  compact = false,
  className = '',
}: ProjectCardProps) {
  const {
    id,
    title,
    description,
    technologies = [],
    status,
    featured: isFeatured,
    github_url: github,
    demo_url: demo,
    image_url: image,
    created_at: createdAt,
  } = project;

  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-adaptive-green" />;
      case 'active':
        return <Clock className="h-4 w-4 text-adaptive-blue" />;
      case 'archived':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'active':
        return 'Em desenvolvimento';
      case 'archived':
        return 'Arquivado';
      default:
        return 'Status desconhecido';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return 'text-adaptive-green border-adaptive-green/20 bg-adaptive-green/10';
      case 'active':
        return 'text-adaptive-blue border-adaptive-blue/20 bg-adaptive-blue/10';
      case 'archived':
        return 'text-yellow-500 border-yellow-500/20 bg-yellow-500/10';
      default:
        return 'text-adaptive-secondary border-adaptive bg-adaptive';
    }
  };

  const cardContent = (
    <div
      className={clsx(
        'card group hover:scale-105 transition-all duration-300 relative',
        featured &&
          'border-adaptive-green/50 bg-gradient-to-br from-adaptive-card to-adaptive-card',
        compact && 'p-4', // Padding menor para compact
        className
      )}
    >
      {/* Featured Badge */}
      {(featured || isFeatured) && !compact && (
        <div className="absolute -top-2 -right-2 bg-adaptive-green text-white dark:text-black px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
          <Star className="h-3 w-3" />
          Destaque
        </div>
      )}

      {/* Project Image - Oculto em compact */}
      {image && !compact && (
        <div className="mb-4 overflow-hidden rounded-lg border border-adaptive">
          <img
            src={image}
            alt={title}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
            loading="lazy"
          />
        </div>
      )}

      {/* Status Badge */}
      <div
        className={clsx(
          'inline-flex items-center gap-1 px-2 py-1 rounded font-medium border mb-3',
          compact ? 'text-xs' : 'text-xs',
          getStatusColor()
        )}
      >
        {getStatusIcon()}
        {getStatusText()}
      </div>

      {/* Title */}
      <h3
        className={clsx(
          'font-semibold text-gradient mb-3 group-hover:text-adaptive-green transition-colors',
          compact ? 'text-base' : 'text-xl'
        )}
      >
        {title}
      </h3>

      {/* Description */}
      <p
        className={clsx(
          'text-adaptive-secondary leading-relaxed mb-4',
          compact ? 'text-sm line-clamp-2' : 'line-clamp-3'
        )}
      >
        {description}
      </p>

      {/* Technologies */}
      <div className="flex flex-wrap gap-2 mb-6">
        {technologies.slice(0, compact ? 3 : technologies.length).map((tech: string) => (
          <span
            key={tech}
            className={clsx(
              'bg-adaptive text-adaptive px-2 py-1 rounded font-medium border border-adaptive',
              compact ? 'text-xs' : 'text-xs'
            )}
          >
            {tech}
          </span>
        ))}
        {compact && technologies.length > 3 && (
          <span className="text-adaptive-secondary text-xs">+{technologies.length - 3}</span>
        )}
      </div>

      {/* Links */}
      <div className="flex items-center gap-4">
        {github && (
          <a
            href={github}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 text-adaptive-secondary hover-adaptive-blue transition-colors"
          >
            <Github className="h-4 w-4" />
            <span className={compact ? 'text-xs' : 'text-sm'}>Código</span>
          </a>
        )}

        {demo && (
          <a
            href={demo}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 text-adaptive-secondary hover-adaptive-green transition-colors"
          >
            <ExternalLink className="h-4 w-4" />
            <span className={compact ? 'text-xs' : 'text-sm'}>Demo</span>
          </a>
        )}

        {!github && !demo && (
          <span className={clsx('text-adaptive-secondary italic', compact ? 'text-xs' : 'text-sm')}>
            Links em breve
          </span>
        )}
      </div>

      {/* Creation Date - Oculto em compact */}
      {createdAt && !compact && (
        <div className="mt-4 pt-4 border-t border-adaptive">
          <span className="text-xs text-adaptive-secondary">
            Criado em {new Date(createdAt).toLocaleDateString('pt-BR')}
          </span>
        </div>
      )}
    </div>
  );

  // Se é compact, não usa motion para evitar animação de entrada
  if (compact) {
    return cardContent;
  }

  // Versão completa com motion
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {cardContent}
    </motion.div>
  );
}

export default ProjectCard;
