import clsx from 'clsx';
import { motion } from 'framer-motion';
import { Calendar, Clock, GitCommit, User } from 'lucide-react';
import type { Devlog } from '../../types';

interface DevlogCardProps {
  devlog: Devlog;
  compact?: boolean;
  className?: string;
}

const DevlogCard: React.FC<DevlogCardProps> = ({ devlog, compact = false, className = '' }) => {
  const {
    id,
    title,
    content,
    created_at,
    author_id,
    // version,
    // tags = [],
  } = devlog;

  // Formatação de data
  const formatDate = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleDateString('pt-BR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Data inválida';
    }
  };

  // Calcular tempo de leitura estimado
  const reading_time = content ? Math.ceil(content.split(' ').length / 200) : 1;

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
    hover: {
      y: -2,
      transition: { duration: 0.2 },
    },
  };

  return (
    <motion.article
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className={clsx(
        'group relative overflow-hidden rounded-xl border border-adaptive-border bg-adaptive-card',
        'hover:border-adaptive-green/50 hover:shadow-lg transition-all duration-300',
        compact && 'p-4',
        !compact && 'p-6',
        className
      )}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-4 text-sm text-adaptive-secondary mb-2">
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <time dateTime={created_at}>{formatDate(created_at)}</time>
            </div>

            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{reading_time} min</span>
            </div>

            <div className="flex items-center space-x-1">
              <User className="w-4 h-4" />
              <span>Admin</span>
            </div>
          </div>

          <h3
            className={clsx(
              'font-bold text-adaptive mb-3 group-hover:text-adaptive-green transition-colors',
              compact ? 'text-lg' : 'text-xl'
            )}
          >
            {title}
          </h3>
        </div>

        {/* Version Badge - Comentado temporariamente */}
        {/* {version && (
          <div className="flex items-center space-x-1 px-2 py-1 bg-adaptive-green/20 text-adaptive-green rounded-full text-xs font-medium">
            <GitCommit className="w-3 h-3" />
            <span>v{version}</span>
          </div>
        )} */}
      </div>

      {/* Content */}
      {content && (
        <div
          className={clsx(
            'text-adaptive-secondary leading-relaxed mb-4',
            compact ? 'text-sm line-clamp-3' : 'text-base'
          )}
        >
          {compact ? content.substring(0, 150) + '...' : content}
        </div>
      )}

      {/* Tags - Comentado temporariamente */}
      {/* {tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {tags.slice(0, compact ? 3 : 6).map((tag: string, index: number) => (
            <span
              key={index}
              className="px-2 py-1 bg-adaptive-green/10 text-adaptive-green rounded-md text-xs font-medium"
            >
              #{tag}
            </span>
          ))}
          {tags.length > (compact ? 3 : 6) && (
            <span className="px-2 py-1 bg-adaptive-border text-adaptive-secondary rounded-md text-xs">
              +{tags.length - (compact ? 3 : 6)}
            </span>
          )}
        </div>
      )} */}

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-adaptive-border">
        <div className="text-sm text-adaptive-secondary">
          <span>Entrada #{id}</span>
        </div>

        <div className="flex items-center space-x-2 text-sm text-adaptive-secondary">
          <GitCommit className="w-4 h-4" />
          <span>Devlog</span>
        </div>
      </div>
    </motion.article>
  );
};

export default DevlogCard;
