import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// ===== INTERFACES =====
interface ThemeContextValue {
  theme: 'dark' | 'light';
  toggleTheme: () => void;
  isDark: boolean;
  isLight: boolean;
  isLoading: boolean;
}

interface ThemeProviderProps {
  children: ReactNode;
}

// ===== CONTEXT =====
const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

// ===== HOOK =====
export function useTheme(): ThemeContextValue {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme deve ser usado dentro de um ThemeProvider');
  }
  return context;
}

// ===== PROVIDER =====
export function ThemeProvider({ children }: ThemeProviderProps): React.ReactElement {
  const [theme, setTheme] = useState<'dark' | 'light'>('dark');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Carrega tema salvo ou detecta preferência do sistema
  useEffect(() => {
    const savedTheme = localStorage.getItem('blueprint-theme') as 'dark' | 'light' | null;
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light';

    const initialTheme = savedTheme || systemTheme;
    setTheme(initialTheme);
    setIsLoading(false);
  }, []);

  // Aplica o tema ao documento
  useEffect(() => {
    if (isLoading) return;

    const root = document.documentElement;

    if (theme === 'dark') {
      root.classList.remove('light');
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
      root.classList.add('light');
    }

    // Salva no localStorage
    localStorage.setItem('blueprint-theme', theme);
  }, [theme, isLoading]);

  // Escuta mudanças na preferência do sistema
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e: MediaQueryListEvent): void => {
      // Só muda automaticamente se não há preferência salva
      if (!localStorage.getItem('blueprint-theme')) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const toggleTheme = (): void => {
    setTheme((prev) => (prev === 'dark' ? 'light' : 'dark'));
  };

  const value: ThemeContextValue = {
    theme,
    toggleTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
    isLoading,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}

// ===== EXPORTS DE TIPOS =====
export type { ThemeContextValue, ThemeProviderProps };
