{"nav": {"home": "Home", "blog": "Blog", "projects": "Projects", "devlog": "Devlog", "about": "About", "themes": "Themes", "contact": "Contact"}, "home": {"title": "BLUEPRINT", "subtitle": "The map. The plan. The sketch of dev life.", "description": "My digital laboratory. My portfolio. My devlog.", "manifesto": "My digital manifesto.", "cta": {"exploreBlog": "Explore Blog", "viewProjects": "View Projects"}, "sections": {"latestPosts": "Latest Articles", "latestPostsDesc": "Thoughts, tutorials and insights about development", "featuredProjects": "Featured Projects", "featuredProjectsDesc": "APIs, bots, SaaS and tools I've built", "viewAllPosts": "View all posts", "viewAllProjects": "View all projects"}, "quickLinks": {"blog": {"title": "Blog", "description": "Technical articles, tutorials and reflections on development.", "cta": "Explore blog"}, "projects": {"title": "Projects", "description": "Complete portfolio with APIs, bots and tools.", "cta": "View portfolio"}, "devlog": {"title": "Devlog", "description": "Public logs of project development.", "cta": "View timeline"}}, "about": {"title": "About Blueprint", "description": "It's not just a blog. It's the blueprint of my digital freedom. Portfolio, laboratory, manifesto and living proof that I'm outside the Matrix.", "cta": "Learn more"}}, "blog": {"title": "Blog", "subtitle": "Articles, tutorials and reflections on development", "loading": "Loading posts...", "noPosts": "No posts found.", "readMore": "Read more", "readingTime": "min read", "categories": "Categories", "tags": "Tags", "allCategories": "All categories", "search": "Search posts...", "searchResults": "Search results", "noResults": "No results found for your search."}, "post": {"backToBlog": "Back to blog", "share": "Share", "sharePost": "Share post", "author": "Author", "publishedOn": "Published on", "readingTime": "Reading time", "categories": "Categories", "tags": "Tags", "likedArticle": "Liked the article? Share it with other developers!", "viewMorePosts": "View more posts"}, "projects": {"title": "Projects", "subtitle": "Portfolio of APIs, bots, SaaS and tools", "loading": "Loading projects...", "noProjects": "No projects found.", "viewProject": "View Project", "viewCode": "View Code", "viewDemo": "View Demo", "technologies": "Technologies", "status": {"active": "Active", "development": "In Development", "maintenance": "Maintenance", "archived": "Archived"}, "categories": {"all": "All", "web": "Web", "mobile": "Mobile", "api": "API", "bot": "Bot", "tool": "Tool", "saas": "SaaS"}}, "devlog": {"title": "Devlog", "subtitle": "Public timeline of project development", "loading": "Loading devlogs...", "noDevlogs": "No entries found", "project": "Project", "learnings": "Learnings", "moreItems": "more...", "featured": "Featured", "allTypes": "All types", "allProjects": "All projects", "allStatus": "All status", "adjustFilters": "Try adjusting the search filters.", "noEntries": "No devlog entries yet."}, "about": {"title": "About", "subtitle": "Who I am and what Blueprint is", "bio": {"title": "About Me", "description1": "<PERSON><PERSON><PERSON> passionate about technology and innovation. I believe in the power of code to transform ideas into reality and solve complex problems.", "description2": "Always looking for new challenges and opportunities to learn. I share knowledge through open source projects and technical articles."}, "blueprint": {"title": "What is Blueprint", "description": "is not just a blog or portfolio. It's my digital laboratory, where I experiment, document and share my development journey.", "features": ["Technical articles and tutorials", "Projects and experiments", "Development logs (devlog)", "Reflections on technology and career"], "quote": "The map. The plan. The sketch of dev life."}, "skills": {"title": "Stack & Skills"}, "contact": {"title": "Let's Talk?", "description": "Interested in collaborating or just exchanging ideas about technology?", "cta": "Get in Touch"}, "theme": {"title": "Theme System", "description": "Demonstration of the adaptive theme system with smooth transitions and consistent color palette between light and dark mode.", "selector": "Theme Selector", "themes": {"system": {"name": "System", "description": "Follows system preference"}, "light": {"name": "Light", "description": "Light and minimalist theme"}, "dark": {"name": "Dark", "description": "Dark cyberpunk theme"}}, "colorPalette": "Color Palette", "components": "Example Components", "buttons": "Buttons", "cards": "Cards", "forms": "Form Elements", "features": {"title": "System Features", "characteristics": "✨ Characteristics", "characteristicsList": ["Smooth transitions between themes", "Preference persistence", "Automatic system detection", "Adaptive CSS classes", "Framer Motion animations"], "palette": "🎨 Adaptive Palette", "paletteList": ["Consistent colors between themes", "Optimized contrast", "Guaranteed accessibility", "Custom CSS variables", "Opacity support"]}}}, "contact": {"title": "Contact", "subtitle": "Let's talk about technology and projects", "form": {"name": "Name", "email": "Email", "subject": "Subject", "message": "Message", "send": "Send Message", "sending": "Sending...", "success": "Message sent successfully!", "error": "Error sending message. Please try again."}, "social": {"title": "Social Media", "description": "You can also find me on social media"}}, "common": {"loading": "Loading...", "error": "Error", "retry": "Try again", "close": "Close", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "view": "View", "download": "Download", "copy": "Copy", "copied": "Copied!", "share": "Share", "like": "Like", "comment": "Comment", "follow": "Follow", "unfollow": "Unfollow", "login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "profile": "Profile", "settings": "Settings", "search": "Search", "filter": "Filter", "sort": "Sort", "date": "Date", "name": "Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot password", "rememberMe": "Remember me", "or": "or", "and": "and", "yes": "Yes", "no": "No", "next": "Next", "previous": "Previous", "first": "First", "last": "Last", "page": "Page", "of": "of", "results": "results", "noResults": "No results found", "selectLanguage": "Select language", "entry": "entry", "entries": "entries", "clearFilters": "Clear filters"}, "footer": {"copyright": "© {{year}} Blueprint Blog. All rights reserved.", "madeWith": "Made with", "and": "and", "by": "by"}}