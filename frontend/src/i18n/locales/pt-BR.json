{"nav": {"home": "Home", "blog": "Blog", "projects": "Projetos", "devlog": "Devlog", "about": "Sobre", "themes": "<PERSON><PERSON>", "contact": "Contato"}, "home": {"title": "BLUEPRINT", "subtitle": "O mapa. A planta. O rascunho da vida dev.", "description": "Meu laboratório digital. Meu portfólio. Meu devlog.", "manifesto": "Meu manifesto digital.", "cta": {"exploreBlog": "Explorar Blog", "viewProjects": "Ver Projetos"}, "sections": {"latestPosts": "Últimos Artigos", "latestPostsDesc": "<PERSON><PERSON><PERSON><PERSON>, tutoriais e insights sobre desenvolvimento", "featuredProjects": "Projetos em Destaque", "featuredProjectsDesc": "APIs, bots, SaaS e ferramentas que construí", "viewAllPosts": "Ver todos os posts", "viewAllProjects": "Ver todos os projetos"}, "quickLinks": {"blog": {"title": "Blog", "description": "Art<PERSON><PERSON>, tutoriais e reflexões sobre desenvolvimento.", "cta": "Explorar blog"}, "projects": {"title": "Projetos", "description": "Portfólio completo com APIs, bots e ferramentas.", "cta": "Ver portfólio"}, "devlog": {"title": "Devlog", "description": "Logs públicos do desenvolvimento dos projetos.", "cta": "Ver timeline"}}, "about": {"title": "Sobre o Blueprint", "description": "Não é só um blog. É o blueprint da minha liberdade digital. Portfólio, laboratório, manifesto e prova viva de que estou fora da Matrix.", "cta": "<PERSON><PERSON><PERSON> mais"}}, "blog": {"title": "Blog", "subtitle": "Artigos, tutoriais e reflexões sobre desenvolvimento", "loading": "Carregando posts...", "noPosts": "Nenhum post encontrado.", "readMore": "Ler mais", "readingTime": "min de leitura", "categories": "Categorias", "tags": "Tags", "allCategories": "<PERSON><PERSON> as categorias", "search": "Buscar posts...", "searchResults": "Resultados da busca", "noResults": "Nenhum resultado encontrado para sua busca."}, "post": {"backToBlog": "Voltar ao blog", "share": "Compartilhar", "sharePost": "Compartilhar post", "author": "Autor", "publishedOn": "Publicado em", "readingTime": "Tempo de leitura", "categories": "Categorias", "tags": "Tags", "likedArticle": "Gostou do artigo? Compartilhe com outros desenvolvedores!", "viewMorePosts": "<PERSON>er mais posts"}, "projects": {"title": "Projetos", "subtitle": "Portfólio de APIs, bots, SaaS e ferramentas", "loading": "Carregando projetos...", "noProjects": "Nenhum projeto encontrado.", "viewProject": "Ver Projeto", "viewCode": "Ver Código", "viewDemo": "Ver Demo", "technologies": "Tecnologias", "status": {"active": "Ativo", "development": "Em Desenvolvimento", "maintenance": "Manutenção", "archived": "Arquivado"}, "categories": {"all": "Todos", "web": "Web", "mobile": "Mobile", "api": "API", "bot": "Bot", "tool": "Ferramenta", "saas": "SaaS"}}, "devlog": {"title": "Devlog", "subtitle": "Timeline pública do desenvolvimento dos projetos", "loading": "Carregando devlogs...", "noDevlogs": "Nenhuma entrada encontrada", "project": "Projeto", "learnings": "Aprendizados", "moreItems": "mais...", "featured": "Destaque", "allTypes": "Todos os tipos", "allProjects": "Todos os projetos", "allStatus": "Todos os status", "adjustFilters": "Tente ajustar os filtros de busca.", "noEntries": "Ainda não há entradas no devlog."}, "about": {"title": "About", "subtitle": "Quem sou eu e o que é o Blueprint", "bio": {"title": "Sobre Mim", "description1": "Desenvolvedor apaixonado por tecnologia e inovação. Acredito no poder do código para transformar ideias em realidade e resolver problemas complexos.", "description2": "Sempre em busca de novos desafios e oportunidades para aprender. Compartilho conhecimento através de projetos open source e artigos técnicos."}, "blueprint": {"title": "O que é o Blueprint", "description": "não é apenas um blog ou portfólio. É meu laboratório digital, onde experimento, documento e compartilho minha jornada no desenvolvimento.", "features": ["Artigos técnicos e tutoriais", "Projetos e experimentos", "Logs de desenvolvimento (devlog)", "Reflexões sobre tecnologia e carreira"], "quote": "O mapa. A planta. O rascunho da vida dev."}, "skills": {"title": "Stack & Skills"}, "contact": {"title": "Vamos Conversar?", "description": "Interessado em colaborar ou apenas trocar uma ideia sobre tecnologia?", "cta": "Entre em Contato"}, "theme": {"title": "<PERSON><PERSON><PERSON> de <PERSON>", "description": "Demonstração do sistema de temas adaptável com transições suaves e paleta de cores consistente entre light e dark mode.", "selector": "<PERSON><PERSON><PERSON>", "themes": {"system": {"name": "Sistema", "description": "Segue a preferência do sistema"}, "light": {"name": "<PERSON><PERSON><PERSON>", "description": "Tema claro e minimalista"}, "dark": {"name": "Escuro", "description": "Tema escuro cyberpunk"}}, "colorPalette": "Paleta de Cores", "components": "Componentes de Exemplo", "buttons": "<PERSON><PERSON><PERSON><PERSON>", "cards": "Cards", "forms": "Elementos de Formulário", "features": {"title": "Recursos do Sistema", "characteristics": "✨ Características", "characteristicsList": ["Transições suaves entre temas", "Persistência da preferência", "Detecção automática do sistema", "Classes CSS adaptáveis", "Animações com Framer Motion"], "palette": "🎨 <PERSON><PERSON><PERSON>", "paletteList": ["Cores consistentes entre temas", "Contraste otimizado", "Acessibilidade garantida", "Variáveis CSS customizadas", "Suporte a opacidade"]}}}, "contact": {"title": "Contato", "subtitle": "Vamos conversar sobre tecnologia e projetos", "form": {"name": "Nome", "email": "Email", "subject": "<PERSON><PERSON><PERSON>", "message": "Mensagem", "send": "Enviar Mensagem", "sending": "Enviando...", "success": "Mensagem enviada com sucesso!", "error": "Erro ao enviar mensagem. Tente novamente."}, "social": {"title": "Redes Sociais", "description": "Você também pode me encontrar nas redes sociais"}}, "common": {"loading": "Carregando...", "error": "Erro", "retry": "Tentar novamente", "close": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "delete": "Excluir", "edit": "<PERSON><PERSON>", "view": "<PERSON>er", "download": "Download", "copy": "Copiar", "copied": "Copiado!", "share": "Compartilhar", "like": "Curtir", "comment": "Comentar", "follow": "<PERSON><PERSON><PERSON>", "unfollow": "<PERSON><PERSON><PERSON>", "login": "Entrar", "logout": "<PERSON><PERSON>", "register": "Cadastrar", "profile": "Perfil", "settings": "Configurações", "search": "Buscar", "filter": "Filtrar", "sort": "Ordenar", "date": "Data", "name": "Nome", "email": "Email", "password": "<PERSON><PERSON>", "confirmPassword": "Confirmar <PERSON>", "forgotPassword": "<PERSON><PERSON><PERSON> <PERSON>ha senha", "rememberMe": "<PERSON><PERSON><PERSON> de mim", "or": "ou", "and": "e", "yes": "<PERSON>m", "no": "Não", "next": "Próximo", "previous": "Anterior", "first": "<PERSON><PERSON>", "last": "Último", "page": "<PERSON><PERSON><PERSON><PERSON>", "of": "de", "results": "resultados", "noResults": "Nenhum resultado encontrado", "selectLanguage": "Selecionar idioma", "entry": "entrada", "entries": "entradas", "clearFilters": "Limpar filtros"}, "footer": {"copyright": "© {{year}} Blueprint Blog. Todos os direitos reservados.", "madeWith": "Feito com", "and": "e", "by": "por"}}