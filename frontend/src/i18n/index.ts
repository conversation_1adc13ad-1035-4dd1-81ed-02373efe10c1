import i18n, { InitOptions } from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next } from 'react-i18next';

// Importar traduções
import enUS from './locales/en-US.json';
import ptBR from './locales/pt-BR.json';

// ===== INTERFACES =====
interface TranslationResource {
  translation: Record<string, any>;
  [key: string]: Record<string, any>;
}

interface DetectionOptions {
  order: string[];
  caches: string[];
  lookupLocalStorage: string;
  lookupFromPathIndex: number;
  lookupFromSubdomainIndex: number;
}

// ===== CONFIGURAÇÕES =====
const resources = {
  'pt-BR': {
    translation: ptBR,
  },
  pt: {
    translation: ptBR,
  },
  'en-US': {
    translation: enUS,
  },
  en: {
    translation: enUS,
  },
};

/**
 * Função para detectar e normalizar o idioma do navegador
 * @returns Código do idioma normalizado
 */
const detectBrowserLanguage = (): string => {
  const browserLang = navigator.language || (navigator as any).userLanguage;

  if (browserLang.startsWith('pt')) {
    return 'pt-BR';
  } else if (browserLang.startsWith('en')) {
    return 'en-US';
  }

  return 'pt-BR'; // Fallback padrão
};

/**
 * Handler para chaves de tradução em falta
 * @param key - Chave da tradução
 * @param defaultValue - Valor padrão
 * @returns Valor padrão ou chave
 */
const parseMissingKeyHandler = (key: string, defaultValue?: string): string => {
  // Só mostra warning se não há valor padrão
  if (!defaultValue) {
    console.warn(`Missing translation key: ${key}`);
  }
  return defaultValue || key;
};

// Configuração do i18n
const i18nConfig: InitOptions = {
  resources,

  // Idioma padrão
  fallbackLng: 'pt-BR',
  lng: detectBrowserLanguage(),

  // Idiomas suportados - incluindo códigos simplificados
  supportedLngs: ['pt-BR', 'pt', 'en-US', 'en'],

  // Configurações de detecção
  detection: {
    order: ['localStorage', 'navigator', 'htmlTag'],
    caches: ['localStorage'],
    lookupLocalStorage: 'blueprint-language',
    // Mapeamento de códigos de idioma
    lookupFromPathIndex: 0,
    lookupFromSubdomainIndex: 0,
  } as DetectionOptions,

  interpolation: {
    escapeValue: false,
  },

  // Configurações para evitar warnings
  react: {
    useSuspense: false,
  },

  // Configurações de debug (desabilitado para produção)
  debug: process.env.NODE_ENV === 'development',

  // Configurações de namespace
  defaultNS: 'translation',
  ns: ['translation'],

  // Configurações de carregamento
  load: 'languageOnly',

  // Configurações de fallback
  fallbackNS: false,

  // Configuração para normalizar códigos de idioma
  cleanCode: true,

  // Configurações de parsing - melhorado para não mostrar warnings excessivos
  parseMissingKeyHandler,

  // Configurações adicionais para melhorar compatibilidade
  compatibilityJSON: 'v4',

  // Configuração para evitar warnings sobre chaves em falta
  saveMissing: false,

  // Configuração para melhorar performance
  initImmediate: false,
};

// ===== INICIALIZAÇÃO =====
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init(i18nConfig)
  .then(() => {
    // Log do idioma atual para debug
    console.log('i18n initialized. Current language:', i18n.language);
  })
  .catch((error: Error) => {
    console.error('Error initializing i18n:', error);
  });

// ===== FUNÇÕES UTILITÁRIAS =====

/**
 * Muda o idioma da aplicação
 * @param language - Código do idioma
 * @returns Promise que resolve quando o idioma é alterado
 */
export const changeLanguage = async (language: string): Promise<void> => {
  try {
    await i18n.changeLanguage(language);
    console.log('Language changed to:', language);
  } catch (error) {
    console.error('Error changing language:', error);
  }
};

/**
 * Obtém o idioma atual
 * @returns Código do idioma atual
 */
export const getCurrentLanguage = (): string => {
  return i18n.language;
};

/**
 * Verifica se um idioma é suportado
 * @param language - Código do idioma
 * @returns True se o idioma é suportado
 */
export const isLanguageSupported = (language: string): boolean => {
  const supportedLngs = i18nConfig.supportedLngs as string[];
  return supportedLngs.includes(language);
};

/**
 * Obtém lista de idiomas suportados
 * @returns Array com códigos dos idiomas suportados
 */
export const getSupportedLanguages = (): string[] => {
  return i18nConfig.supportedLngs as string[];
};

/**
 * Traduz uma chave com parâmetros opcionais
 * @param key - Chave da tradução
 * @param options - Opções de tradução
 * @returns Texto traduzido
 */
export const translate = (key: string, options?: any): string => {
  return i18n.t(key, options) as string;
};

// ===== EXPORT DEFAULT =====
export default i18n;

// ===== EXPORTS DE TIPOS =====
export type { TranslationResource, DetectionOptions };
