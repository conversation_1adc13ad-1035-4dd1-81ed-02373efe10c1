// 🎯 Tipos principais baseados no backend Rust

// ===== POSTS =====
export interface Post {
  id: string;
  title: string;
  content: string;
  slug: string;
  status: 'draft' | 'published' | 'archived';
  published_at: string | null;
  created_at: string;
  updated_at: string;
  author_id: string;
  language: 'pt' | 'en';
  views: number;
  featured?: boolean;
  excerpt?: string;
  tags?: string[];
  category?: string;
}

// ===== API RESPONSES =====
export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  error?: string;
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
}

// ===== QUERY PARAMETERS =====
export interface PostsQuery {
  page?: number;
  limit?: number;
  status?: string;
  author?: string;
  language?: 'pt' | 'en';
  featured?: boolean;
  category?: string;
  search?: string;
  sortBy?: 'created_at' | 'published_at' | 'views' | 'title';
  sortOrder?: 'asc' | 'desc';
}

// ===== USER & AUTH =====
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'editor' | 'author';
  avatar?: string;
  bio?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  expires_at: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// ===== PROJECTS =====
export interface Project {
  id: string;
  title: string;
  description: string;
  slug: string;
  status: 'active' | 'completed' | 'archived';
  technologies: string[];
  github_url?: string;
  demo_url?: string;
  image_url?: string;
  featured: boolean;
  created_at: string;
  updated_at: string;
}

// ===== DEVLOGS =====
export interface Devlog {
  id: string;
  title: string;
  content: string;
  project_id: string;
  author_id: string;
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
  project?: Project; // Populated when needed
}

// ===== CONTENT TYPES =====
export interface ContentItem {
  id: string;
  type: 'post' | 'project' | 'devlog';
  title: string;
  slug: string;
  excerpt?: string;
  image?: string;
  date: string;
  author?: string;
  tags?: string[];
  featured?: boolean;
}

// ===== FORM TYPES =====
export interface PostFormData {
  title: string;
  content: string;
  slug: string;
  status: Post['status'];
  language: Post['language'];
  featured?: boolean;
  excerpt?: string;
  tags?: string[];
  category?: string;
}

export interface ProjectFormData {
  title: string;
  description: string;
  slug: string;
  status: Project['status'];
  technologies: string[];
  github_url?: string;
  demo_url?: string;
  image_url?: string;
  featured: boolean;
}

// ===== UTILITY TYPES =====
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface AsyncState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface PaginatedData<T = any> {
  items: T[];
  pagination: PaginationInfo;
}

// ===== THEME & UI =====
export type Theme = 'cyberpunk' | 'neon' | 'matrix' | 'synthwave';
export type Language = 'pt' | 'en';

export interface AppSettings {
  theme: Theme;
  language: Language;
  darkMode: boolean;
  animations: boolean;
}

// ===== SEARCH & FILTERS =====
export interface SearchFilters {
  query?: string;
  type?: 'all' | 'posts' | 'projects' | 'devlogs';
  language?: Language;
  dateRange?: {
    start: string;
    end: string;
  };
  tags?: string[];
  author?: string;
}

export interface SearchResult {
  id: string;
  type: 'post' | 'project' | 'devlog';
  title: string;
  excerpt: string;
  url: string;
  date: string;
  relevance: number;
  highlights?: string[];
}

// ===== ERROR TYPES =====
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// ===== ANALYTICS =====
export interface ViewStats {
  total_views: number;
  unique_views: number;
  daily_views: Array<{
    date: string;
    views: number;
  }>;
}

export interface PostAnalytics extends Post {
  stats: ViewStats;
  engagement: {
    avg_read_time: number;
    bounce_rate: number;
    shares: number;
  };
}

// ===== EXPORT ALL =====
export type {
  // Re-export for convenience
  Post as BlogPost,
  User as Author,
  ApiResponse as Response,
};

// ===== RE-EXPORTS FROM OTHER FILES =====
export * from './components';
export * from './api';
export * from './utils';
