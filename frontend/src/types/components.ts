// 🎯 Tipos para Props de Componentes React

import { ReactNode, HTMLAttributes, ButtonHTMLAttributes, InputHTMLAttributes } from 'react';
import { Post, Project, Devlog, User, Theme, Language, LoadingState } from './index';

// ===== LAYOUT COMPONENTS =====
export interface LayoutProps {
  children: ReactNode;
  className?: string;
}

export interface HeaderProps {
  transparent?: boolean;
  fixed?: boolean;
  className?: string;
}

export interface FooterProps {
  minimal?: boolean;
  className?: string;
}

export interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

// ===== CONTENT COMPONENTS =====
export interface PostCardProps {
  post: Post;
  variant?: 'default' | 'featured' | 'compact' | 'minimal';
  showAuthor?: boolean;
  showDate?: boolean;
  showExcerpt?: boolean;
  showTags?: boolean;
  showViews?: boolean;
  className?: string;
  onClick?: (post: Post) => void;
}

export interface PostListProps {
  posts: Post[];
  loading?: boolean;
  error?: string | null;
  variant?: 'grid' | 'list';
  showPagination?: boolean;
  onPostClick?: (post: Post) => void;
  className?: string;
}

export interface ProjectCardProps {
  project: Project;
  variant?: 'default' | 'featured' | 'compact';
  showTechnologies?: boolean;
  showLinks?: boolean;
  className?: string;
  onClick?: (project: Project) => void;
}

export interface DevlogCardProps {
  devlog: Devlog;
  showProject?: boolean;
  showAuthor?: boolean;
  className?: string;
  onClick?: (devlog: Devlog) => void;
}

// ===== UI COMPONENTS =====
export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  children: ReactNode;
}

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  variant?: 'default' | 'outline' | 'filled';
}

export interface TextareaProps extends HTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  rows?: number;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export interface SelectProps {
  label?: string;
  error?: string;
  helperText?: string;
  options: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: ReactNode;
  className?: string;
}

export interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}

export interface BadgeProps {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md';
  children: ReactNode;
  className?: string;
}

export interface AvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fallback?: string;
  className?: string;
}

// ===== FORM COMPONENTS =====
export interface FormFieldProps {
  name: string;
  label?: string;
  error?: string;
  required?: boolean;
  children: ReactNode;
  className?: string;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit?: (value: string) => void;
  placeholder?: string;
  loading?: boolean;
  suggestions?: string[];
  className?: string;
}

export interface FilterBarProps {
  filters: Record<string, any>;
  onChange: (filters: Record<string, any>) => void;
  options: Array<{
    key: string;
    label: string;
    type: 'select' | 'multiselect' | 'date' | 'toggle';
    options?: Array<{ value: string; label: string }>;
  }>;
  className?: string;
}

// ===== EDITOR COMPONENTS =====
export interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: string;
  preview?: boolean;
  toolbar?: boolean;
  className?: string;
}

export interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  theme?: 'dark' | 'light';
  readOnly?: boolean;
  height?: string;
  className?: string;
}

// ===== NAVIGATION COMPONENTS =====
export interface BreadcrumbProps {
  items: Array<{
    label: string;
    href?: string;
    current?: boolean;
  }>;
  separator?: ReactNode;
  className?: string;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisible?: number;
  className?: string;
}

export interface TabsProps {
  tabs: Array<{
    id: string;
    label: string;
    content: ReactNode;
    disabled?: boolean;
  }>;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  variant?: 'default' | 'pills' | 'underline';
  className?: string;
}

// ===== THEME COMPONENTS =====
export interface ThemeSwitcherProps {
  currentTheme: Theme;
  onThemeChange: (theme: Theme) => void;
  className?: string;
}

export interface LanguageSwitcherProps {
  currentLanguage: Language;
  onLanguageChange: (language: Language) => void;
  className?: string;
}

// ===== ANIMATION COMPONENTS =====
export interface AnimatedCounterProps {
  value: number;
  duration?: number;
  format?: (value: number) => string;
  className?: string;
}

export interface FadeInProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  className?: string;
}

// ===== UTILITY PROPS =====
export interface WithClassName {
  className?: string;
}

export interface WithChildren {
  children: ReactNode;
}

export interface WithLoading {
  loading?: boolean;
  loadingState?: LoadingState;
}

export interface WithError {
  error?: string | null;
}

// ===== COMMON COMBINATIONS =====
export type BaseComponentProps = WithClassName & WithChildren;
export type AsyncComponentProps = WithLoading & WithError;
export type InteractiveComponentProps = BaseComponentProps & {
  disabled?: boolean;
  onClick?: () => void;
};

// ===== EVENT HANDLERS =====
export interface ComponentEventHandlers {
  onLoad?: () => void;
  onError?: (error: Error) => void;
  onSuccess?: () => void;
  onCancel?: () => void;
  onSubmit?: () => void;
  onReset?: () => void;
}
