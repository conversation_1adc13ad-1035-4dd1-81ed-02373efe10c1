// 🎯 Tipos específicos para APIs e Hooks

import { Post, Project, Devlog, User, PostsQuery, ApiResponse, PaginationInfo } from './index';

// ===== API SERVICE TYPES =====
export interface ApiServiceConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
  retries?: number;
}

export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
  timeout?: number;
}

// ===== POSTS API =====
export interface PostsApiService {
  getPosts(query?: PostsQuery): Promise<ApiResponse<Post[]>>;
  getPost(id: string): Promise<ApiResponse<Post>>;
  getPostBySlug(slug: string): Promise<ApiResponse<Post>>;
  createPost(data: Partial<Post>): Promise<ApiResponse<Post>>;
  updatePost(id: string, data: Partial<Post>): Promise<ApiResponse<Post>>;
  deletePost(id: string): Promise<ApiResponse<void>>;
  incrementViews(id: string): Promise<ApiResponse<void>>;
}

// ===== PROJECTS API =====
export interface ProjectsApiService {
  getProjects(): Promise<ApiResponse<Project[]>>;
  getProject(id: string): Promise<ApiResponse<Project>>;
  getProjectBySlug(slug: string): Promise<ApiResponse<Project>>;
  createProject(data: Partial<Project>): Promise<ApiResponse<Project>>;
  updateProject(id: string, data: Partial<Project>): Promise<ApiResponse<Project>>;
  deleteProject(id: string): Promise<ApiResponse<void>>;
}

// ===== DEVLOGS API =====
export interface DevlogsApiService {
  getDevlogs(projectId?: string): Promise<ApiResponse<Devlog[]>>;
  getDevlog(id: string): Promise<ApiResponse<Devlog>>;
  createDevlog(data: Partial<Devlog>): Promise<ApiResponse<Devlog>>;
  updateDevlog(id: string, data: Partial<Devlog>): Promise<ApiResponse<Devlog>>;
  deleteDevlog(id: string): Promise<ApiResponse<void>>;
}

// ===== AUTH API =====
export interface AuthApiService {
  login(email: string, password: string): Promise<ApiResponse<{ user: User; token: string }>>;
  logout(): Promise<ApiResponse<void>>;
  register(data: { email: string; password: string; name: string }): Promise<ApiResponse<User>>;
  getCurrentUser(): Promise<ApiResponse<User>>;
  refreshToken(): Promise<ApiResponse<{ token: string }>>;
  forgotPassword(email: string): Promise<ApiResponse<void>>;
  resetPassword(token: string, password: string): Promise<ApiResponse<void>>;
}

// ===== HOOK TYPES =====

// usePosts Hook
export interface UsePostsOptions extends PostsQuery {
  enabled?: boolean;
  refetchOnMount?: boolean;
  refetchInterval?: number;
}

export interface UsePostsReturn {
  posts: Post[];
  loading: boolean;
  error: string | null;
  pagination: PaginationInfo | null;
  refetch: () => Promise<void>;
  loadMore: () => Promise<void>;
  hasMore: boolean;
}

// usePost Hook
export interface UsePostOptions {
  enabled?: boolean;
  refetchOnMount?: boolean;
}

export interface UsePostReturn {
  post: Post | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  incrementViews: () => Promise<void>;
}

// useProjects Hook
export interface UseProjectsReturn {
  projects: Project[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// useAuth Hook
export interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (data: { email: string; password: string; name: string }) => Promise<void>;
  refreshToken: () => Promise<void>;
}

// useLocalStorage Hook
export interface UseLocalStorageReturn<T> {
  value: T;
  setValue: (value: T | ((prev: T) => T)) => void;
  removeValue: () => void;
}

// useDebounce Hook
export interface UseDebounceOptions {
  delay: number;
  leading?: boolean;
  trailing?: boolean;
}

// useInfiniteScroll Hook
export interface UseInfiniteScrollOptions {
  threshold?: number;
  rootMargin?: string;
  enabled?: boolean;
}

export interface UseInfiniteScrollReturn {
  ref: React.RefObject<HTMLElement>;
  isIntersecting: boolean;
}

// useSearch Hook
export interface UseSearchOptions {
  debounceDelay?: number;
  minQueryLength?: number;
  enabled?: boolean;
}

export interface UseSearchReturn {
  query: string;
  setQuery: (query: string) => void;
  results: any[];
  loading: boolean;
  error: string | null;
  search: (query: string) => Promise<void>;
  clearResults: () => void;
}

// ===== CONTEXT TYPES =====

// Theme Context
export interface ThemeContextValue {
  theme: string;
  setTheme: (theme: string) => void;
  isDark: boolean;
  toggleDarkMode: () => void;
}

// Language Context
export interface LanguageContextValue {
  language: string;
  setLanguage: (language: string) => void;
  t: (key: string, params?: Record<string, any>) => string;
}

// Auth Context
export interface AuthContextValue extends UseAuthReturn {
  // Additional context-specific methods
}

// ===== FORM TYPES =====
export interface FormState<T = any> {
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

export interface FormActions<T = any> {
  setValue: (field: keyof T, value: any) => void;
  setError: (field: keyof T, error: string) => void;
  setTouched: (field: keyof T, touched: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  reset: () => void;
  submit: () => Promise<void>;
}

export interface UseFormOptions<T = any> {
  initialValues: T;
  validationSchema?: any;
  onSubmit: (values: T) => Promise<void> | void;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

export interface UseFormReturn<T = any> extends FormState<T>, FormActions<T> {}

// ===== UTILITY TYPES =====
export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number;
  serialize?: boolean;
}

export interface RetryOptions {
  maxRetries: number;
  delay: number;
  backoff?: 'linear' | 'exponential';
}

// ===== ERROR HANDLING =====
export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
}

export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; reset: () => void }>;
  onError?: (error: Error, errorInfo: any) => void;
}

// ===== PERFORMANCE TYPES =====
export interface LazyComponentProps {
  fallback?: React.ComponentType;
  delay?: number;
}

export interface VirtualizedListProps<T = any> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
}

// ===== ANALYTICS TYPES =====
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: number;
  userId?: string;
  sessionId?: string;
}

export interface AnalyticsService {
  track: (event: AnalyticsEvent) => void;
  identify: (userId: string, traits?: Record<string, any>) => void;
  page: (name: string, properties?: Record<string, any>) => void;
}

// ===== WEBSOCKET TYPES =====
export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: number;
  id?: string;
}

export interface WebSocketOptions {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  send: (message: any) => void;
  lastMessage: any;
  error: Event | null;
}

// ===== EXPORT HELPERS =====
export type ApiMethod<T = any> = (...args: any[]) => Promise<ApiResponse<T>>;
export type HookReturn<T = any> = T & { loading: boolean; error: string | null };
export type ApiEventHandler<T = any> = (event: T) => void;
export type ApiAsyncFunction<T = any> = (...args: any[]) => Promise<T>;
