import { Filter, Search, Zap } from 'lucide-react';
import { useState } from 'react';
import ProjectCard from '../components/projects/ProjectCard';
import { useProjects } from '../hooks/useContent';
import { Project } from '../types';

function Projects() {
  const { projects, loading, error } = useProjects();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedTech, setSelectedTech] = useState('');

  // Get unique technologies and statuses
  const allTechnologies = [...new Set(projects.flatMap((p) => p.technologies))].sort();
  const allStatuses = [...new Set(projects.map((p) => p.status))].sort();

  // Filter projects
  const filteredProjects = projects.filter((project) => {
    const matchesSearch =
      !searchTerm ||
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !selectedStatus || project.status === selectedStatus;

    const matchesTech = !selectedTech || project.technologies.includes(selectedTech);

    return matchesSearch && matchesStatus && matchesTech;
  });

  const getStatusLabel = (status: Project['status']) => {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'active':
        return 'Em desenvolvimento';
      case 'archived':
        return 'Arquivado';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="pt-16 min-h-screen">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="text-adaptive-green font-mono">{'>'} Carregando projetos...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-16 min-h-screen">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="card text-center">
            <h2 className="text-2xl font-semibold text-red-500 mb-4">Erro ao carregar projetos</h2>
            <p className="text-adaptive-secondary">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-16 min-h-screen">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Zap className="h-12 w-12 text-adaptive-green mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gradient">Projetos</h1>
          </div>
          <p className="text-xl text-adaptive-secondary">
            APIs, bots, SaaS e ferramentas que construí
          </p>
          <p className="text-adaptive-secondary mt-2">
            {projects.length} {projects.length === 1 ? 'projeto' : 'projetos'} no portfólio
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-adaptive-secondary" />
            <input
              type="text"
              placeholder="Buscar projetos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-adaptive-card border border-adaptive rounded-lg text-adaptive placeholder-adaptive-secondary focus-adaptive-green"
            />
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-adaptive-card border border-adaptive rounded-lg px-3 py-2 text-adaptive focus-adaptive-green"
            >
              <option value="">Todos os status</option>
              {allStatuses.map((status) => (
                <option key={status} value={status}>
                  {getStatusLabel(status)}
                </option>
              ))}
            </select>

            {/* Technology Filter */}
            <select
              value={selectedTech}
              onChange={(e) => setSelectedTech(e.target.value)}
              className="bg-adaptive-card border border-adaptive rounded-lg px-3 py-2 text-adaptive focus-adaptive-green"
            >
              <option value="">Todas as tecnologias</option>
              {allTechnologies.map((tech) => (
                <option key={tech} value={tech}>
                  {tech}
                </option>
              ))}
            </select>

            {/* Clear Filters */}
            {(searchTerm || selectedStatus || selectedTech) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedStatus('');
                  setSelectedTech('');
                }}
                className="px-4 py-2 text-adaptive-secondary hover-adaptive-green transition-colors"
              >
                Limpar filtros
              </button>
            )}
          </div>
        </div>

        {/* Projects Grid */}
        {filteredProjects.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project) => (
              <ProjectCard key={project.id} project={project} featured={project.featured} />
            ))}
          </div>
        ) : (
          <div className="card text-center">
            <Filter className="h-12 w-12 text-adaptive-secondary mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gradient mb-4">Nenhum projeto encontrado</h2>
            <p className="text-adaptive-secondary">
              {searchTerm || selectedStatus || selectedTech
                ? 'Tente ajustar os filtros de busca.'
                : 'Ainda não há projetos no portfólio.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default Projects;
