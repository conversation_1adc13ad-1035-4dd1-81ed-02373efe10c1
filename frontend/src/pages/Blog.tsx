import { Filter, Search, Terminal } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import PostCard from '../components/blog/PostCard';
import { usePosts } from '../hooks/usePosts';

function Blog() {
  const { t, i18n } = useTranslation();
  const { posts, loading, error } = usePosts({
    status: 'published',
    language: i18n.language === 'pt-BR' ? 'pt' : 'en',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTag, setSelectedTag] = useState('');

  // Filter posts based on search and filters
  const filteredPosts = posts.filter((post) => {
    const matchesSearch =
      !searchTerm ||
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (post.excerpt && post.excerpt.toLowerCase().includes(searchTerm.toLowerCase())) ||
      post.content.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = !selectedCategory || post.category === selectedCategory;

    const matchesTag = !selectedTag || (post.tags && post.tags.includes(selectedTag));

    return matchesSearch && matchesCategory && matchesTag;
  });

  // Extrair categorias únicas dos posts
  const categories = [...new Set(posts.map((post) => post.category).filter(Boolean))];

  // Extrair tags únicas dos posts
  const tags = [...new Set(posts.flatMap((post) => post.tags || []))];

  if (loading) {
    return (
      <div className="pt-16 min-h-screen">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="text-adaptive-blue font-mono">{'>'} Carregando posts...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-16 min-h-screen">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="card text-center">
            <h2 className="text-2xl font-semibold text-red-500 mb-4">Erro ao carregar posts</h2>
            <p className="text-adaptive-secondary">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-16 min-h-screen">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Terminal className="h-12 w-12 text-adaptive-blue mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gradient">Blog</h1>
          </div>
          <p className="text-xl text-adaptive-secondary">
            Pensamentos, tutoriais e insights sobre desenvolvimento
          </p>
          <p className="text-adaptive-secondary mt-2">
            {posts.length} {posts.length === 1 ? 'artigo' : 'artigos'} publicados
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-adaptive-secondary" />
            <input
              type="text"
              placeholder="Buscar artigos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-adaptive-card border border-adaptive rounded-lg text-adaptive placeholder-adaptive-secondary focus-adaptive-blue"
            />
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-adaptive-card border border-adaptive rounded-lg px-3 py-2 text-adaptive focus-adaptive-blue"
            >
              <option value="">Todas as categorias</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            {/* Tag Filter */}
            <select
              value={selectedTag}
              onChange={(e) => setSelectedTag(e.target.value)}
              className="bg-adaptive-card border border-adaptive rounded-lg px-3 py-2 text-adaptive focus-adaptive-blue"
            >
              <option value="">Todas as tags</option>
              {tags.map((tag) => (
                <option key={tag} value={tag}>
                  #{tag}
                </option>
              ))}
            </select>

            {/* Clear Filters */}
            {(searchTerm || selectedCategory || selectedTag) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('');
                  setSelectedTag('');
                }}
                className="px-4 py-2 text-adaptive-secondary hover:text-adaptive-blue transition-colors"
              >
                Limpar filtros
              </button>
            )}
          </div>
        </div>

        {/* Posts Grid */}
        {filteredPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.map((post) => (
              <PostCard key={post.slug} post={post} featured={post.status === 'published'} />
            ))}
          </div>
        ) : (
          <div className="card text-center">
            <Filter className="h-12 w-12 text-adaptive-secondary mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gradient mb-4">Nenhum post encontrado</h2>
            <p className="text-adaptive-secondary">
              {searchTerm || selectedCategory || selectedTag
                ? 'Tente ajustar os filtros de busca.'
                : 'Ainda não há posts publicados.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default Blog;
