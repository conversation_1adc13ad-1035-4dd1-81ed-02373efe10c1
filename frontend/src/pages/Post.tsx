import { ArrowLef<PERSON>, Calendar, Clock, Eye, Folder, Share2, Tag, User } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, Navigate, useParams } from 'react-router-dom';
import MarkdownRenderer from '../components/blog/MarkdownRenderer';
import SEO from '../components/common/SEO';
import { usePosts } from '../hooks/usePosts';

// 🎯 TypeScript interfaces
interface PostData {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  reading_time?: number;
  tags?: string[]; // ✅ Corrigido: tags pode ser undefined
  categories?: Array<{
    category?: { name: string };
    name?: string;
  }>;
  created_at: string;
  published_at?: string | null; // ✅ Corrigido: pode ser null ou undefined
  featured_image?: string;
  views?: number;
  author_name?: string;
}

const Post: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { getPostBySlug } = usePosts();
  const [post, setPost] = useState<PostData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPost = async () => {
      if (!slug) return;

      setLoading(true);
      const { data, error } = await getPostBySlug(slug);

      if (error) {
        setError(error);
      } else if (data) {
        setPost({
          ...data,
          tags: data.tags || [], // ✅ Garantir que tags seja sempre um array
          categories: (data as any).categories || [],
        });
      }

      setLoading(false);
    };

    loadPost();
  }, [slug]); // ✅ Remover dependências das funções

  if (loading) {
    return (
      <div className="pt-16 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="text-neon-blue font-mono">{'>'} Carregando post...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return <Navigate to="/blog" replace />;
  }

  // Adaptar para estrutura do Supabase
  const {
    title,
    content,
    excerpt,
    reading_time,
    tags = [],
    categories = [],
    created_at,
    published_at,
    featured_image,
    views = 0,
    author_name,
  } = post;

  // Nome do autor vem diretamente do campo author_name
  const authorName = author_name || 'Autor Desconhecido';

  // Formatação de data com validação
  const formatDate = (dateString: string | null | undefined): string | null => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return null;
      return date.toLocaleDateString('pt-BR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return null;
    }
  };

  const formattedDate = formatDate(published_at) || formatDate(created_at) || 'Data não disponível';

  // Formatação do tempo de leitura
  const readingTime = reading_time ? `${reading_time} min de leitura` : null;

  // Extrair nomes das categorias
  const categoryNames = categories.map((cat) => cat.category?.name || cat.name).filter(Boolean);

  const sharePost = async (): Promise<void> => {
    const url = window.location.href;
    const text = `${title} - ${excerpt}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text,
          url,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(url);
        // You could show a toast notification here
        console.log('URL copied to clipboard');
      } catch (err) {
        console.log('Error copying to clipboard:', err);
      }
    }
  };

  return (
    <>
      <SEO
        title={title}
        description={excerpt ?? 'Artigo do blog'}
        type="article"
        url={window.location.href}
        publishedTime={published_at ?? new Date().toISOString()}
        tags={tags}
      />

      <div className="pt-16 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Back to Blog */}
          <Link
            to="/blog"
            className="inline-flex items-center text-dark-text-secondary hover:text-neon-blue transition-colors mb-8 group"
          >
            <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
            Voltar ao blog
          </Link>

          {/* Featured Image */}
          {featured_image && (
            <div className="mb-8 overflow-hidden rounded-lg border border-adaptive">
              <img
                src={featured_image}
                alt={title}
                className="w-full h-64 md:h-96 object-cover"
                loading="lazy"
              />
            </div>
          )}

          {/* Article Header */}
          <header className="mb-8">
            {/* Categories */}
            {categoryNames.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {categoryNames.map((category, index) => (
                  <span
                    key={index}
                    className="bg-neon-blue/10 text-neon-blue px-3 py-1 rounded-full text-sm font-medium border border-neon-blue/20 flex items-center gap-1"
                  >
                    <Folder className="h-3 w-3" />
                    {category}
                  </span>
                ))}
              </div>
            )}

            {/* Title */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gradient mb-6 leading-tight">
              {title}
            </h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-dark-text-secondary mb-6">
              {/* Author */}
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>Por {authorName}</span>
              </div>

              {formattedDate && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{formattedDate}</span>
                </div>
              )}

              {readingTime && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>{readingTime}</span>
                </div>
              )}

              {views > 0 && (
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  <span>{views} visualizações</span>
                </div>
              )}

              {/* Share Button */}
              <button
                onClick={sharePost}
                className="flex items-center gap-2 hover:text-neon-blue transition-colors"
                title="Compartilhar post"
              >
                <Share2 className="h-4 w-4" />
                <span>Compartilhar</span>
              </button>
            </div>

            {/* Tags */}
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <span
                    key={tag}
                    className="bg-dark-bg text-dark-text-secondary px-2 py-1 rounded text-sm border border-dark-border flex items-center gap-1"
                  >
                    <Tag className="h-3 w-3" />#{tag}
                  </span>
                ))}
              </div>
            )}
          </header>

          {/* Article Content */}
          <article className="card">
            <MarkdownRenderer content={content} />
          </article>

          {/* Article Footer */}
          <footer className="mt-12 pt-8 border-t border-dark-border">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <p className="text-dark-text-secondary text-sm">
                  Gostou do artigo? Compartilhe com outros desenvolvedores!
                </p>
              </div>

              <div className="flex gap-4">
                <button onClick={sharePost} className="btn-secondary text-sm">
                  Compartilhar
                </button>
                <Link to="/blog" className="btn-primary text-sm">
                  Ver mais posts
                </Link>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </>
  );
};

export default Post;
