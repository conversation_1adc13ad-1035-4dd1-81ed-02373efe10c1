import { <PERSON>, <PERSON>, Palette, <PERSON> } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import SEO from '../components/common/SEO';
import ThemeDemo from '../components/ui/ThemeDemo';

const Themes: React.FC = () => {
  const { t } = useTranslation();

  return (
    <>
      <SEO
        title="Themes"
        description="Configurações de tema e aparência do Blueprint Blog"
        keywords={['temas', 'dark mode', 'light mode', 'configurações', 'aparência']}
        image="/og-themes.jpg"
        url="/themes"
        publishedTime=""
        modifiedTime=""
        canonical="/themes"
        schema={{}}
      />
      <div className="pt-16 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <Palette className="h-12 w-12 text-adaptive-purple mr-4" />
              <h1 className="text-4xl md:text-5xl font-bold text-gradient">Themes</h1>
            </div>
            <p className="text-xl text-adaptive-secondary">
              Customize a aparência do Blueprint para sua preferência
            </p>
          </div>

          <div className="space-y-8">
            {/* Theme Selector */}
            <div className="card">
              <h2 className="text-2xl font-semibold text-gradient mb-6 flex items-center">
                <Monitor className="h-6 w-6 mr-2" />
                Escolha seu tema
              </h2>

              {/* Theme Demo Component */}
              <ThemeDemo />
            </div>

            {/* Theme Showcase */}
            <div className="card">
              <h2 className="text-2xl font-semibold text-gradient mb-6">Comparação dos temas</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Dark Theme Preview */}
                <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
                  <div className="flex items-center mb-4">
                    <Moon className="h-5 w-5 text-blue-400 mr-2" />
                    <h3 className="text-lg font-semibold text-white">Dark Theme</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-700 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-700 rounded w-5/6"></div>
                  </div>
                  <div className="mt-4 flex space-x-2">
                    <div className="w-8 h-8 bg-blue-500 rounded"></div>
                    <div className="w-8 h-8 bg-purple-500 rounded"></div>
                    <div className="w-8 h-8 bg-green-500 rounded"></div>
                  </div>
                </div>

                {/* Light Theme Preview */}
                <div className="bg-white rounded-lg p-6 border border-gray-200">
                  <div className="flex items-center mb-4">
                    <Sun className="h-5 w-5 text-orange-500 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900">Light Theme</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                  </div>
                  <div className="mt-4 flex space-x-2">
                    <div className="w-8 h-8 bg-blue-500 rounded"></div>
                    <div className="w-8 h-8 bg-purple-500 rounded"></div>
                    <div className="w-8 h-8 bg-green-500 rounded"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="card">
              <h2 className="text-2xl font-semibold text-gradient mb-6">Recursos dos temas</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-semibold text-adaptive">🌙 Dark Theme</h3>
                  <ul className="space-y-2 text-sm text-adaptive-secondary">
                    <li>• Reduz fadiga ocular em ambientes escuros</li>
                    <li>• Economiza bateria em telas OLED</li>
                    <li>• Ideal para programação noturna</li>
                    <li>• Cores vibrantes com alto contraste</li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-adaptive">☀️ Light Theme</h3>
                  <ul className="space-y-2 text-sm text-adaptive-secondary">
                    <li>• Melhor legibilidade em ambientes claros</li>
                    <li>• Ideal para leitura durante o dia</li>
                    <li>• Cores suaves e confortáveis</li>
                    <li>• Aparência limpa e profissional</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Auto Theme */}
            <div className="card bg-gradient-to-r from-adaptive-blue/10 to-adaptive-purple/10 border-adaptive-blue/20">
              <div className="text-center">
                <Monitor className="h-12 w-12 text-adaptive-blue mx-auto mb-4" />
                <h2 className="text-2xl font-semibold text-gradient mb-4">Tema Automático</h2>
                <p className="text-adaptive-secondary mb-6">
                  O Blueprint detecta automaticamente sua preferência do sistema e ajusta o tema
                  conforme necessário. Você pode alternar manualmente a qualquer momento usando o
                  botão no canto superior direito.
                </p>
                <div className="flex items-center justify-center space-x-4 text-sm text-adaptive-secondary">
                  <div className="flex items-center">
                    <Sun className="h-4 w-4 mr-1" />
                    <span>6:00 - 18:00</span>
                  </div>
                  <div className="text-adaptive-border">→</div>
                  <div className="flex items-center">
                    <Moon className="h-4 w-4 mr-1" />
                    <span>18:00 - 6:00</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Themes;
