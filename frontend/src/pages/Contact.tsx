import { Mail, Github, Linkedin, Twitter, MessageCircle } from 'lucide-react';

function Contact() {
  const contactMethods = [
    {
      name: 'Email',
      description: 'Para conversas mais formais e propostas',
      href: 'mailto:<EMAIL>',
      icon: Mail,
      color: 'text-neon-blue',
    },
    {
      name: 'GitHub',
      description: 'Veja meus projetos e contribuições',
      href: 'https://github.com',
      icon: Github,
      color: 'text-dark-text',
    },
    {
      name: 'LinkedIn',
      description: 'Conecte-se profissionalmente',
      href: 'https://linkedin.com',
      icon: Linkedin,
      color: 'text-neon-blue',
    },
    {
      name: 'Twitter',
      description: 'Acompanhe pensamentos rápidos e updates',
      href: 'https://twitter.com',
      icon: Twitter,
      color: 'text-neon-blue',
    },
  ];

  return (
    <div className="pt-16 min-h-screen">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <MessageCircle className="h-12 w-12 text-neon-green mr-4" />
            <h1 className="text-4xl md:text-5xl font-bold text-gradient">Contact</h1>
          </div>
          <p className="text-xl text-dark-text-secondary">
            Bora trocar ideia, construir ou hackear o sistema
          </p>
        </div>

        {/* Intro */}
        <div className="card text-center mb-8">
          <h2 className="text-2xl font-semibold text-gradient mb-4">Vamos Conversar?</h2>
          <p className="text-dark-text-secondary text-lg leading-relaxed">
            Sempre aberto para discutir projetos interessantes, oportunidades de colaboração, ou
            simplesmente trocar ideias sobre tecnologia e desenvolvimento.
          </p>
        </div>

        {/* Contact Methods */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {contactMethods.map((method) => {
            const Icon = method.icon;
            return (
              <a
                key={method.name}
                href={method.href}
                target="_blank"
                rel="noopener noreferrer"
                className="card group hover:scale-105 transition-all duration-300"
              >
                <div className="flex items-start space-x-4">
                  <div className={`${method.color} group-hover:animate-glow`}>
                    <Icon className="h-8 w-8" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gradient mb-2">{method.name}</h3>
                    <p className="text-dark-text-secondary">{method.description}</p>
                  </div>
                </div>
              </a>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="card text-center mt-8">
          <h3 className="text-xl font-semibold text-gradient mb-4">Prefere um papo mais direto?</h3>
          <p className="text-dark-text-secondary mb-6">Mande um email que respondo rapidinho!</p>
          <a href="mailto:<EMAIL>" className="btn-primary">
            Enviar Email
          </a>
        </div>
      </div>
    </div>
  );
}

export default Contact;
