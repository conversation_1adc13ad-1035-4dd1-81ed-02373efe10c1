import { Code, Terminal, User, Zap } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import SEO from '../components/common/SEO';

const About: React.FC = () => {
  const { t } = useTranslation();

  const skills: string[] = [
    'React',
    'Node.js',
    'TypeScript',
    'Python',
    'PostgreSQL',
    'Docker',
    'AWS',
    'Git',
    'Linux',
    'API Design',
    'Microservices',
    'DevOps',
  ];

  return (
    <>
      <SEO
        title={t('about.title', 'About')}
        description={t('about.subtitle', 'Quem sou eu e o que é o Blueprint')}
        keywords={['sobre', 'desenvolvedor', 'blueprint', 'tecnologia', 'programação']}
        image="/og-about.jpg"
        url="/about"
        publishedTime=""
        modifiedTime=""
        canonical="/about"
        schema={{}}
      />
      <div className="pt-16 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <User className="h-12 w-12 text-adaptive-blue mr-4" />
              <h1 className="text-4xl md:text-5xl font-bold text-gradient">
                {t('about.title', 'About')}
              </h1>
            </div>
            <p className="text-xl text-adaptive-secondary">
              {t('about.subtitle', 'Quem sou eu e o que é o Blueprint')}
            </p>
          </div>

          <div className="space-y-8">
            {/* Bio */}
            <div className="card">
              <h2 className="text-2xl font-semibold text-gradient mb-4 flex items-center">
                <Code className="h-6 w-6 mr-2" />
                {t('about.bio.title', 'Sobre Mim')}
              </h2>
              <div className="prose prose-adaptive max-w-none">
                <p className="text-adaptive-secondary leading-relaxed">
                  {t(
                    'about.bio.content',
                    'Desenvolvedor apaixonado por tecnologia e inovação. Este é o Blueprint - meu laboratório digital onde compartilho conhecimentos, experimentos e projetos.'
                  )}
                </p>
                <p className="text-adaptive-secondary leading-relaxed">
                  {t(
                    'about.bio.mission',
                    'Acredito que o conhecimento deve ser compartilhado. Aqui você encontra desde tutoriais técnicos até reflexões sobre o mundo da programação.'
                  )}
                </p>
              </div>
            </div>

            {/* Skills */}
            <div className="card">
              <h2 className="text-2xl font-semibold text-gradient mb-4 flex items-center">
                <Terminal className="h-6 w-6 mr-2" />
                {t('about.skills.title', 'Tecnologias')}
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {skills.map((skill) => (
                  <div
                    key={skill}
                    className="px-4 py-2 bg-adaptive-blue/10 text-adaptive-blue rounded-lg text-center font-medium hover:bg-adaptive-blue/20 transition-colors"
                  >
                    {skill}
                  </div>
                ))}
              </div>
            </div>

            {/* Blueprint */}
            <div className="card">
              <h2 className="text-2xl font-semibold text-gradient mb-4 flex items-center">
                <Zap className="h-6 w-6 mr-2" />
                {t('about.blueprint.title', 'O que é o Blueprint?')}
              </h2>
              <div className="prose prose-adaptive max-w-none">
                <p className="text-adaptive-secondary leading-relaxed">
                  {t(
                    'about.blueprint.content',
                    'Blueprint é mais que um blog - é um laboratório digital. Aqui documento minha jornada no desenvolvimento, compartilho projetos e exploro novas tecnologias.'
                  )}
                </p>
                <div className="grid md:grid-cols-2 gap-6 mt-6">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-adaptive">📝 Blog</h3>
                    <p className="text-sm text-adaptive-secondary">
                      Artigos técnicos, tutoriais e reflexões sobre desenvolvimento
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-adaptive">🚀 Projetos</h3>
                    <p className="text-sm text-adaptive-secondary">
                      Experimentos, ferramentas e aplicações que desenvolvo
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-adaptive">📜 Devlog</h3>
                    <p className="text-sm text-adaptive-secondary">
                      Diário de desenvolvimento com atualizações frequentes
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-adaptive">🎨 Temas</h3>
                    <p className="text-sm text-adaptive-secondary">
                      Demonstração de diferentes estilos visuais
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact CTA */}
            <div className="card bg-gradient-to-r from-adaptive-blue/10 to-adaptive-purple/10 border-adaptive-blue/20">
              <div className="text-center">
                <h2 className="text-2xl font-semibold text-gradient mb-4">
                  {t('about.contact.title', 'Vamos conversar?')}
                </h2>
                <p className="text-adaptive-secondary mb-6">
                  {t(
                    'about.contact.subtitle',
                    'Tem alguma dúvida, sugestão ou quer trocar uma ideia sobre tecnologia?'
                  )}
                </p>
                <a
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 bg-adaptive-blue text-white rounded-lg hover:bg-adaptive-blue/80 transition-colors font-medium"
                >
                  {t('about.contact.button', 'Entre em contato')}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default About;
