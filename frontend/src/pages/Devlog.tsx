import { Code2, Filter, Search } from 'lucide-react';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import SEO from '../components/common/SEO';
import DevlogCard from '../components/devlog/DevlogCard';
import { useDevlogs } from '../hooks/useContent';
import type { Devlog } from '../types';

const Devlog: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { devlogs, loading, error } = useDevlogs();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');

  // Get unique values for filters
  const allTypes = ['milestone', 'feature', 'bugfix', 'update', 'idea', 'release'];
  const allProjects = [
    ...new Set(devlogs.map((d) => d.project?.title || '').filter(Boolean)),
  ].sort();
  const allStatuses = ['draft', 'published'];

  // Filter devlogs
  const filteredDevlogs = devlogs.filter((devlog) => {
    const matchesSearch =
      !searchTerm ||
      devlog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (devlog.content && devlog.content.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (devlog.project?.title &&
        devlog.project.title.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesType = !selectedType || devlog.project?.status === selectedType;
    const matchesProject = !selectedProject || devlog.project?.title === selectedProject;
    const matchesStatus = !selectedStatus || devlog.status === selectedStatus;

    return matchesSearch && matchesType && matchesProject && matchesStatus;
  });

  // Labels da interface - traduzíveis
  const getTypeLabel = (type: string) => {
    const labels = {
      pt: {
        active: 'Ativo',
        completed: 'Concluído',
        archived: 'Arquivado',
      },
      en: {
        active: 'Active',
        completed: 'Completed',
        archived: 'Archived',
      },
    };

    const lang = i18n.language.startsWith('pt') ? 'pt' : 'en';
    return labels[lang][type as keyof typeof labels.pt] || type;
  };

  const getStatusLabel = (status: string) => {
    const labels = {
      pt: {
        draft: 'Rascunho',
        published: 'Publicado',
      },
      en: {
        draft: 'Draft',
        published: 'Published',
      },
    };

    const lang = i18n.language.startsWith('pt') ? 'pt' : 'en';
    return labels[lang][status as keyof typeof labels.pt] || status;
  };

  if (loading) {
    return (
      <>
        <SEO
          title={t('devlog.title', 'Devlog')}
          description={t('devlog.subtitle', 'Timeline pública do desenvolvimento dos projetos')}
        />
        <div className="pt-16 min-h-screen">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <div className="text-adaptive-purple font-mono">
                {'>'} {t('devlog.loading', 'Carregando devlogs...')}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <SEO
          title={t('devlog.title', 'Devlog')}
          description={t('devlog.subtitle', 'Timeline pública do desenvolvimento dos projetos')}
        />
        <div className="pt-16 min-h-screen">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="card text-center">
              <h2 className="text-2xl font-semibold text-red-500 mb-4">
                {t('common.error', 'Erro')}
              </h2>
              <p className="text-adaptive-secondary">{error}</p>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <SEO
        title={t('devlog.title', 'Devlog')}
        description={t('devlog.subtitle', 'Timeline pública do desenvolvimento dos projetos')}
        keywords="devlog, desenvolvimento, projetos, timeline, programação"
      />
      <div className="pt-16 min-h-screen">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <Code2 className="h-12 w-12 text-adaptive-purple mr-4" />
              <h1 className="text-4xl md:text-5xl font-bold text-gradient">
                {t('devlog.title', 'Devlog')}
              </h1>
            </div>
            <p className="text-xl text-adaptive-secondary">
              {t('devlog.subtitle', 'Timeline pública do desenvolvimento dos projetos')}
            </p>
            <p className="text-adaptive-secondary mt-2">
              {devlogs.length}{' '}
              {devlogs.length === 1
                ? t('common.entry', 'entrada')
                : t('common.entries', 'entradas')}
            </p>
          </div>

          {/* Search and Filters */}
          <div className="mb-8 space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-adaptive-secondary" />
              <input
                type="text"
                placeholder={t('common.search', 'Buscar') + '...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-adaptive-card border border-adaptive rounded-lg text-adaptive placeholder-adaptive-secondary focus-adaptive-purple"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              {/* Type Filter */}
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="bg-adaptive-card border border-adaptive rounded-lg px-3 py-2 text-adaptive focus-adaptive-purple"
              >
                <option value="">{t('devlog.allTypes', 'Todos os tipos')}</option>
                {allTypes.map((type) => (
                  <option key={type} value={type}>
                    {getTypeLabel(type)}
                  </option>
                ))}
              </select>

              {/* Project Filter */}
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="bg-adaptive-card border border-adaptive rounded-lg px-3 py-2 text-adaptive focus-adaptive-purple"
              >
                <option value="">{t('devlog.allProjects', 'Todos os projetos')}</option>
                {allProjects.map((project) => (
                  <option key={project} value={project}>
                    {project}
                  </option>
                ))}
              </select>

              {/* Status Filter */}
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="bg-adaptive-card border border-adaptive rounded-lg px-3 py-2 text-adaptive focus-adaptive-purple"
              >
                <option value="">{t('devlog.allStatus', 'Todos os status')}</option>
                {allStatuses.map((status) => (
                  <option key={status} value={status}>
                    {getStatusLabel(status)}
                  </option>
                ))}
              </select>

              {/* Clear Filters */}
              {(searchTerm || selectedType || selectedProject || selectedStatus) && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedType('');
                    setSelectedProject('');
                    setSelectedStatus('');
                  }}
                  className="px-4 py-2 text-adaptive-secondary hover:text-adaptive-purple transition-colors"
                >
                  {t('common.clearFilters', 'Limpar filtros')}
                </button>
              )}
            </div>
          </div>

          {/* Timeline */}
          {filteredDevlogs.length > 0 ? (
            <div className="space-y-6">
              {filteredDevlogs.map((devlog, index) => (
                <div key={devlog.id} className="relative">
                  {/* Timeline line */}
                  {index < filteredDevlogs.length - 1 && (
                    <div className="absolute left-6 top-16 w-0.5 h-full bg-gradient-to-b from-adaptive-purple/50 to-transparent"></div>
                  )}

                  {/* Timeline dot */}
                  <div className="absolute left-4 top-8 w-4 h-4 bg-adaptive-purple rounded-full border-2 border-adaptive shadow-lg shadow-adaptive-purple/20"></div>

                  {/* Content */}
                  <div className="ml-12">
                    <DevlogCard devlog={devlog} />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="card text-center">
              <Filter className="h-12 w-12 text-adaptive-secondary mx-auto mb-4" />
              <h2 className="text-2xl font-semibold text-gradient mb-4">
                {t('devlog.noDevlogs', 'Nenhuma entrada encontrada')}
              </h2>
              <p className="text-adaptive-secondary">
                {searchTerm || selectedType || selectedProject || selectedStatus
                  ? t('devlog.adjustFilters', 'Tente ajustar os filtros de busca.')
                  : t('devlog.noEntries', 'Ainda não há entradas no devlog.')}
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Devlog;
