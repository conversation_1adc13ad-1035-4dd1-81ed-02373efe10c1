import { ArrowRight, Code2, Terminal, Zap } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import PostCard from '../components/blog/PostCard';
import SEO from '../components/common/SEO';
import ProjectCard from '../components/projects/ProjectCard';
import { useFeaturedProjects } from '../hooks/useContent';
import { usePosts } from '../hooks/usePosts';

interface FeaturedPostsProps {
  limit?: number;
}

interface FeaturedProjectsProps {
  limit?: number;
}

interface PopularPostsProps {
  limit?: number;
}

// Featured Posts Component
function FeaturedPosts({ limit = 4 }: FeaturedPostsProps) {
  console.log('🏠 [FeaturedPosts] Renderizando com limit:', limit);

  const { posts, loading, error } = usePosts({
    status: 'published',
    limit: limit,
    orderBy: 'published_at',
    sortOrder: 'desc',
  });

  console.log('🏠 [FeaturedPosts] Estado:', {
    posts: posts.length,
    loading,
    error,
  });

  if (loading) {
    console.log('⏳ [FeaturedPosts] Mostrando loading...');
    return (
      <div className="text-center">
        <div className="text-adaptive-blue font-mono text-sm">{'>'} Carregando posts...</div>
      </div>
    );
  }

  if (error || posts.length === 0) {
    console.log('❌ [FeaturedPosts] Erro ou sem posts:', {
      error,
      postsLength: posts.length,
    });
    return (
      <div className="text-center">
        <p className="text-adaptive-secondary text-sm">Nenhum post em destaque no momento.</p>
      </div>
    );
  }

  console.log('✅ [FeaturedPosts] Renderizando', posts.length, 'posts');
  return (
    <div className="space-y-4">
      {posts.map((post) => (
        <PostCard key={post.slug} post={post} featured compact />
      ))}
    </div>
  );
}

// Featured Projects Component
function FeaturedProjects({ limit = 3 }: FeaturedProjectsProps) {
  const { projects, loading, error } = useFeaturedProjects(limit);

  if (loading) {
    return (
      <div className="text-center">
        <div className="text-adaptive-green font-mono text-sm">{'>'} Carregando projetos...</div>
      </div>
    );
  }

  if (error || projects.length === 0) {
    return (
      <div className="text-center">
        <p className="text-adaptive-secondary text-sm">Nenhum projeto em destaque no momento.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {projects.slice(0, limit).map((project) => (
        <ProjectCard key={project.id} project={project} featured compact />
      ))}
    </div>
  );
}

// Popular Posts Component (usando dados reais da API)
function PopularPosts({ limit = 3 }: PopularPostsProps) {
  console.log('📈 [PopularPosts] Renderizando com limit:', limit);

  const { posts, loading, error } = usePosts({
    status: 'published',
    limit: limit,
    orderBy: 'views', // Ordenar por views para posts populares
    sortOrder: 'desc',
  });

  console.log('📈 [PopularPosts] Estado:', {
    posts: posts.length,
    loading,
    error,
  });

  if (loading) {
    console.log('⏳ [PopularPosts] Mostrando loading...');
    return (
      <div className="text-center">
        <div className="text-adaptive-purple font-mono text-sm">
          {'>'} Carregando posts populares...
        </div>
      </div>
    );
  }

  if (error || posts.length === 0) {
    console.log('❌ [PopularPosts] Erro ou sem posts:', {
      error,
      postsLength: posts.length,
    });
    return (
      <div className="text-center">
        <p className="text-adaptive-secondary text-sm">Nenhum post popular no momento.</p>
        <div className="mt-2">
          <div className="inline-flex items-center px-3 py-1 bg-adaptive-purple/10 border border-adaptive-purple/20 rounded text-xs text-adaptive-purple">
            🚧 Analytics em breve
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {posts.map((post, index) => {
        const icons = ['🔥', '⭐', '💎', '🚀', '💡'];
        const icon = icons[index % icons.length];

        return (
          <div key={post.id} className="card p-4">
            <div className="flex items-start space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-adaptive-blue/20 to-adaptive-purple/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <span className="text-lg">{icon}</span>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-adaptive-primary line-clamp-2 mb-1">
                  {post.title}
                </h4>
                {post.excerpt && (
                  <p className="text-xs text-adaptive-secondary line-clamp-1 mb-2">
                    {post.excerpt}
                  </p>
                )}
                <div className="flex items-center space-x-2 text-xs text-adaptive-secondary">
                  <span>📊 {post.views || 0}</span>
                  <span>⏱️ {Math.ceil(post.content.length / 1000)}min</span>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

function Home() {
  const { t } = useTranslation();

  return (
    <>
      <SEO
        title={t('nav.home')}
        description={t('home.description') + ' ' + t('home.manifesto')}
        keywords="desenvolvimento web, programação, blog tech, portfólio, projetos"
        type="website"
      />
      <div className="pt-16">
        {/* Hero Section - ULTRA COMPACTO */}
        <section className="h-[15vh] min-h-[200px] flex items-center justify-center relative overflow-hidden">
          <div className="absolute inset-0 bg-blueprint opacity-10"></div>

          <div className="relative z-10 text-center max-w-3xl mx-auto px-4">
            <Code2 className="h-8 w-8 text-adaptive-blue mx-auto mb-2" />
            <h1 className="text-2xl md:text-4xl font-bold text-gradient mb-1">{t('home.title')}</h1>
            <p className="text-sm md:text-base text-adaptive-secondary font-mono">
              {t('home.subtitle')}
            </p>
          </div>
        </section>

        {/* GRID PRINCIPAL - ABOVE THE FOLD */}
        <section className="py-8 px-4 sm:px-6 lg:px-8 bg-adaptive-card/30">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Coluna 1: Posts Recentes */}
              <div className="space-y-4">
                <div className="text-center lg:text-left">
                  <h2 className="text-xl md:text-2xl font-bold text-gradient mb-2">
                    📝 {t('home.sections.latestPosts')}
                  </h2>
                  <p className="text-adaptive-secondary text-sm mb-4">
                    {t('home.sections.latestPostsDesc')}
                  </p>
                </div>

                <FeaturedPosts limit={4} />

                <div className="text-center">
                  <Link to="/blog" className="btn-secondary btn-sm">
                    Ver todos os posts
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Link>
                </div>
              </div>

              {/* Coluna 2: Projetos em Destaque */}
              <div className="space-y-4">
                <div className="text-center lg:text-left">
                  <h2 className="text-xl md:text-2xl font-bold text-gradient mb-2">
                    🚀 {t('home.sections.featuredProjects')}
                  </h2>
                  <p className="text-adaptive-secondary text-sm mb-4">
                    {t('home.sections.featuredProjectsDesc')}
                  </p>
                </div>

                <FeaturedProjects limit={3} />

                <div className="text-center">
                  <Link to="/projects" className="btn-secondary btn-sm">
                    Ver todos os projetos
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Link>
                </div>
              </div>

              {/* Coluna 3: Posts Populares */}
              <div className="space-y-4">
                <div className="text-center lg:text-left">
                  <h2 className="text-xl md:text-2xl font-bold text-gradient mb-2">
                    📈 Posts Populares
                  </h2>
                  <p className="text-adaptive-secondary text-sm mb-4">
                    Os mais acessados pelos leitores
                  </p>
                </div>

                <PopularPosts limit={3} />
              </div>
            </div>
          </div>
        </section>

        {/* About Section - COM LOGO GRANDE E CTAs */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            {/* Logo Grande */}
            <div className="mb-8">
              <Code2 className="h-16 w-16 text-adaptive-blue mx-auto mb-4 animate-glow" />
              <h2 className="text-3xl md:text-4xl font-bold text-gradient mb-4">
                {t('home.about.title')}
              </h2>
            </div>

            {/* Descrição */}
            <p className="text-lg text-adaptive-secondary leading-relaxed mb-8 max-w-2xl mx-auto">
              {t('home.about.description')}
            </p>

            {/* CTAs - Explorar Blog + Conhecer mais */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link to="/blog" className="btn-primary group">
                {t('home.cta.exploreBlog')}
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link to="/about" className="btn-secondary">
                {t('home.about.cta')}
              </Link>
            </div>
          </div>
        </section>

        {/* Quick Links - Após About */}
        <section className="py-8 px-4 sm:px-6 lg:px-8 bg-adaptive-card/30">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="card p-4">
                <div className="flex items-center mb-2">
                  <Terminal className="h-4 w-4 text-adaptive-blue mr-2" />
                  <h3 className="font-semibold text-gradient text-sm">
                    {t('home.quickLinks.blog.title')}
                  </h3>
                </div>
                <p className="text-adaptive-secondary mb-2 text-xs">
                  {t('home.quickLinks.blog.description')}
                </p>
                <Link
                  to="/blog"
                  className="text-adaptive-blue hover:text-adaptive-green transition-colors inline-flex items-center text-xs"
                >
                  {t('home.quickLinks.blog.cta')}
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <div className="card p-4">
                <div className="flex items-center mb-2">
                  <Zap className="h-4 w-4 text-adaptive-green mr-2" />
                  <h3 className="font-semibold text-gradient text-sm">
                    {t('home.quickLinks.projects.title')}
                  </h3>
                </div>
                <p className="text-adaptive-secondary mb-2 text-xs">
                  {t('home.quickLinks.projects.description')}
                </p>
                <Link
                  to="/projects"
                  className="text-adaptive-blue hover:text-adaptive-green transition-colors inline-flex items-center text-xs"
                >
                  {t('home.quickLinks.projects.cta')}
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <div className="card p-4">
                <div className="flex items-center mb-2">
                  <Code2 className="h-4 w-4 text-adaptive-purple mr-2" />
                  <h3 className="font-semibold text-gradient text-sm">
                    {t('home.quickLinks.devlog.title')}
                  </h3>
                </div>
                <p className="text-adaptive-secondary mb-2 text-xs">
                  {t('home.quickLinks.devlog.description')}
                </p>
                <Link
                  to="/devlog"
                  className="text-adaptive-blue hover:text-adaptive-green transition-colors inline-flex items-center text-xs"
                >
                  {t('home.quickLinks.devlog.cta')}
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Link>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}

export default Home;
