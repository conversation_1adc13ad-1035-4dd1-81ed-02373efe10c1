import { useCallback, useEffect, useState } from 'react';
import { apiService } from '../services/api';
import type { Post, PostsQuery, ApiResponse } from '@/types';

// ===== INTERFACES =====
interface UsePostsOptions extends PostsQuery {
  orderBy?: 'published_at' | 'created_at' | 'updated_at' | 'views' | 'title';
  orderDirection?: 'asc' | 'desc';
}

interface UsePostsReturn {
  posts: Post[];
  loading: boolean;
  error: string | null;
  createPost: (postData: any) => Promise<{ data: Post | null; error: string | null }>;
  updatePost: (id: string, postData: any) => Promise<{ data: Post | null; error: string | null }>;
  deletePost: (id: string) => Promise<{ error: string | null }>;
  getPostBySlug: (slug: string) => Promise<{ data: Post | null; error: string | null }>;
  refetch: () => Promise<void>;
}

interface UseFeaturedPostsReturn {
  posts: Post[];
  loading: boolean;
  error: string | null;
}

// ===== HOOK PRINCIPAL =====
export const usePosts = (options: UsePostsOptions = {}): UsePostsReturn => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const {
    status = 'published',
    language = 'pt',
    limit = null,
    orderBy = 'published_at',
    orderDirection = 'desc',
  } = options;

  useEffect(() => {
    fetchPosts();
  }, [status, language, limit, orderBy, orderDirection]);

  const fetchPosts = async (): Promise<void> => {
    try {
      console.log('🔍 [usePosts] Iniciando busca de posts com opções:', {
        status,
        language,
        limit,
        orderBy,
        orderDirection,
      });
      setLoading(true);
      setError(null);

      const params: Record<string, any> = {
        status,
        language,
        limit,
        order_by: orderBy,
        order_direction: orderDirection,
      };

      // Remove parâmetros nulos/undefined
      Object.keys(params).forEach((key) => {
        if (params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      console.log('📤 [usePosts] Parâmetros da API:', params);
      const response: ApiResponse<Post[]> = await apiService.getPosts(params);
      console.log('📥 [usePosts] Resposta da API:', response);

      // API retorna { data: [...], pagination: {...} }
      const posts = response?.data || [];
      console.log('✅ [usePosts] Posts processados:', posts.length, 'posts encontrados');
      console.log('📋 [usePosts] Primeiro post:', posts[0]);

      setPosts(posts);
    } catch (err) {
      console.error('❌ [usePosts] Erro ao buscar posts:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  const createPost = async (
    postData: any
  ): Promise<{ data: Post | null; error: string | null }> => {
    try {
      const response: ApiResponse<Post> = await apiService.createPost(postData);
      const data = response.data;

      if (data) {
        // Atualizar lista local
        setPosts((prev) => [data, ...prev]);
        return { data, error: null };
      }

      return { data: null, error: 'Erro ao criar post' };
    } catch (err) {
      console.error('Erro ao criar post:', err);
      return {
        data: null,
        error: err instanceof Error ? err.message : 'Erro desconhecido',
      };
    }
  };

  const updatePost = async (
    id: string,
    postData: any
  ): Promise<{ data: Post | null; error: string | null }> => {
    try {
      const response: ApiResponse<Post> = await apiService.updatePost(id, postData);
      const data = response.data;

      if (data) {
        // Atualizar na lista local
        setPosts((prev) => prev.map((post) => (post.id === id ? { ...post, ...data } : post)));
        return { data, error: null };
      }

      return { data: null, error: 'Erro ao atualizar post' };
    } catch (err) {
      console.error('Erro ao atualizar post:', err);
      return {
        data: null,
        error: err instanceof Error ? err.message : 'Erro desconhecido',
      };
    }
  };

  const deletePost = async (id: string): Promise<{ error: string | null }> => {
    try {
      await apiService.deletePost(id);

      // Remover da lista local
      setPosts((prev) => prev.filter((post) => post.id !== id));
      return { error: null };
    } catch (err) {
      console.error('Erro ao deletar post:', err);
      return {
        error: err instanceof Error ? err.message : 'Erro desconhecido',
      };
    }
  };

  const getPostBySlug = useCallback(
    async (slug: string): Promise<{ data: Post | null; error: string | null }> => {
      try {
        const response: ApiResponse<Post> = await apiService.getPostBySlug(slug);
        // API retorna { data: {...}, status: "success" }
        return { data: response?.data || null, error: null };
      } catch (err) {
        console.error('Erro ao buscar post por slug:', err);
        return {
          data: null,
          error: err instanceof Error ? err.message : 'Erro desconhecido',
        };
      }
    },
    []
  );

  return {
    posts,
    loading,
    error,
    createPost,
    updatePost,
    deletePost,
    getPostBySlug,
    refetch: fetchPosts,
  };
};

// ===== HOOK PARA POSTS EM DESTAQUE =====
export const useFeaturedPosts = (limit: number = 3): UseFeaturedPostsReturn => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedPosts = async (): Promise<void> => {
      try {
        setLoading(true);
        setError(null);

        const params: PostsQuery = {
          status: 'published',
          featured: true,
          limit,
          sortBy: 'published_at',
          sortOrder: 'desc',
        };

        const response: ApiResponse<Post[]> = await apiService.getPosts(params);
        setPosts(response?.data || []);
      } catch (err) {
        console.error('Erro ao buscar posts em destaque:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedPosts();
  }, [limit]);

  return { posts, loading, error };
};

// ===== EXPORTS DE TIPOS =====
export type { UsePostsOptions, UsePostsReturn, UseFeaturedPostsReturn };
