import { useEffect, useState } from 'react';
import {
  getAllPosts,
  getAllProjects,
  getFeaturedPosts,
  getFeaturedProjects,
  getPostBySlug,
  getAllDevlogs,
  getDevlogById,
  getRecentDevlogs,
} from '../utils/content';
import type { Post, Project, Devlog } from '@/types';

// ===== INTERFACES =====
interface UsePostsReturn {
  posts: Post[];
  loading: boolean;
  error: string | null;
}

interface UsePostReturn {
  post: Post | null;
  loading: boolean;
  error: string | null;
}

interface UseProjectsReturn {
  projects: Project[];
  loading: boolean;
  error: string | null;
}

interface UseDevlogsReturn {
  devlogs: Devlog[];
  loading: boolean;
  error: string | null;
}

interface UseDevlogReturn {
  devlog: Devlog | null;
  loading: boolean;
  error: string | null;
}

// ===== HOOKS PARA POSTS =====

/**
 * Hook para gerenciar posts do blog
 * @returns Estado dos posts e métodos
 */
export function usePosts(): UsePostsReturn {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPosts(): Promise<void> {
      try {
        setLoading(true);
        setError(null);
        const allPosts = await getAllPosts();
        setPosts(allPosts);
      } catch (err) {
        console.error('Erro ao buscar posts:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchPosts();
  }, []);

  return { posts, loading, error };
}

/**
 * Hook para obter um post específico
 * @param slug - Slug do post
 * @returns Estado do post e métodos
 */
export function usePost(slug: string | undefined): UsePostReturn {
  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPost(): Promise<void> {
      if (!slug) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const postData = await getPostBySlug(slug);
        setPost(postData);

        if (!postData) {
          setError('Post não encontrado');
        }
      } catch (err) {
        console.error('Erro ao buscar post:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchPost();
  }, [slug]);

  return { post, loading, error };
}

/**
 * Hook para obter posts em destaque
 * @param limit - Número de posts para buscar
 * @returns Estado dos posts em destaque
 */
export function useFeaturedPosts(limit: number = 3): UsePostsReturn {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchFeaturedPosts(): Promise<void> {
      try {
        setLoading(true);
        setError(null);
        const featuredPosts = await getFeaturedPosts(limit);
        setPosts(featuredPosts);
      } catch (err) {
        console.error('Erro ao buscar posts em destaque:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedPosts();
  }, [limit]);

  return { posts, loading, error };
}

// ===== HOOKS PARA PROJETOS =====

/**
 * Hook para gerenciar projetos
 * @returns Estado dos projetos e métodos
 */
export function useProjects(): UseProjectsReturn {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProjects(): Promise<void> {
      try {
        setLoading(true);
        setError(null);
        const allProjects = await getAllProjects();
        setProjects(allProjects);
      } catch (err) {
        console.error('Erro ao buscar projetos:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchProjects();
  }, []);

  return { projects, loading, error };
}

/**
 * Hook para obter projetos em destaque
 * @param limit - Número de projetos para buscar
 * @returns Estado dos projetos em destaque
 */
export function useFeaturedProjects(limit: number = 3): UseProjectsReturn {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchFeaturedProjects(): Promise<void> {
      try {
        setLoading(true);
        setError(null);
        const featuredProjects = await getFeaturedProjects(limit);
        setProjects(featuredProjects);
      } catch (err) {
        console.error('Erro ao buscar projetos em destaque:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedProjects();
  }, [limit]);

  return { projects, loading, error };
}

// ===== HOOKS PARA DEVLOGS =====

/**
 * Hook para gerenciar devlogs
 * @returns Estado dos devlogs e métodos
 */
export function useDevlogs(): UseDevlogsReturn {
  const [devlogs, setDevlogs] = useState<Devlog[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchDevlogs(): Promise<void> {
      try {
        setLoading(true);
        setError(null);
        const allDevlogs = await getAllDevlogs();
        setDevlogs(allDevlogs);
      } catch (err) {
        console.error('Erro ao buscar devlogs:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchDevlogs();
  }, []);

  return { devlogs, loading, error };
}

/**
 * Hook para obter um devlog específico
 * @param id - ID do devlog
 * @returns Estado do devlog e métodos
 */
export function useDevlog(id: string | undefined): UseDevlogReturn {
  const [devlog, setDevlog] = useState<Devlog | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchDevlog(): Promise<void> {
      if (!id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const devlogData = await getDevlogById(id);
        setDevlog(devlogData);

        if (!devlogData) {
          setError('Devlog não encontrado');
        }
      } catch (err) {
        console.error('Erro ao buscar devlog:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchDevlog();
  }, [id]);

  return { devlog, loading, error };
}

/**
 * Hook para obter devlogs recentes
 * @param limit - Número de devlogs para buscar
 * @returns Estado dos devlogs recentes
 */
export function useRecentDevlogs(limit: number = 5): UseDevlogsReturn {
  const [devlogs, setDevlogs] = useState<Devlog[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchRecentDevlogs(): Promise<void> {
      try {
        setLoading(true);
        setError(null);
        const recentDevlogs = await getRecentDevlogs(limit);
        setDevlogs(recentDevlogs);
      } catch (err) {
        console.error('Erro ao buscar devlogs recentes:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchRecentDevlogs();
  }, [limit]);

  return { devlogs, loading, error };
}

// ===== EXPORTS DE TIPOS =====
export type { UsePostsReturn, UsePostReturn, UseProjectsReturn, UseDevlogsReturn, UseDevlogReturn };
