import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { apiService } from '../services/api';
import type { User } from '@/types';

// ===== INTERFACES =====
interface AuthContextValue {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<AuthResult>;
  signUp: (email: string, password: string, metadata?: UserMetadata) => Promise<AuthResult>;
  signOut: () => Promise<{ error: string | null }>;
  resetPassword: (email: string) => Promise<{ error: string | null }>;
  updatePassword: (newPassword: string) => Promise<{ error: string | null }>;
}

interface AuthResult {
  user: User | null;
  error: string | null;
}

interface UserMetadata {
  name?: string;
  [key: string]: any;
}

interface AuthProviderProps {
  children: ReactNode;
}

// ===== CONTEXT =====
const AuthContext = createContext<AuthContextValue | undefined>(undefined);

// ===== PROVIDER =====
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Verificar sessão atual
    getSession();
  }, []);

  const getSession = async (): Promise<void> => {
    try {
      if (apiService.isAuthenticated()) {
        const response = await apiService.getCurrentUser();
        setUser(response.data || null);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Erro ao obter sessão:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string): Promise<AuthResult> => {
    try {
      setLoading(true);
      const response = await apiService.login(email, password);

      if (response.data) {
        apiService.setAuthToken(response.data.token);
        setUser(response.data.user);
        return { user: response.data.user, error: null };
      }

      return { user: null, error: response.message || 'Erro no login' };
    } catch (error) {
      console.error('Erro no login:', error);
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (
    email: string,
    password: string,
    metadata: UserMetadata = {}
  ): Promise<AuthResult> => {
    try {
      setLoading(true);
      const response = await apiService.register({
        email,
        password,
        name: metadata.name || '',
        ...metadata,
      });

      if (response.data) {
        return { user: response.data, error: null };
      }

      return { user: null, error: response.message || 'Erro no registro' };
    } catch (error) {
      console.error('Erro no registro:', error);
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async (): Promise<{ error: string | null }> => {
    try {
      setLoading(true);
      await apiService.logout();
      apiService.clearAuthToken();
      setUser(null);
      return { error: null };
    } catch (error) {
      console.error('Erro no logout:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string): Promise<{ error: string | null }> => {
    try {
      // TODO: Implementar reset de senha no backend
      console.log('Reset password para:', email);
      return { error: null };
    } catch (error) {
      console.error('Erro ao resetar senha:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  };

  const updatePassword = async (newPassword: string): Promise<{ error: string | null }> => {
    try {
      // TODO: Implementar update de senha no backend
      console.log('Update password');
      return { error: null };
    } catch (error) {
      console.error('Erro ao atualizar senha:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  };

  const value: AuthContextValue = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// ===== HOOK =====
export const useAuth = (): AuthContextValue => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

// ===== EXPORT DEFAULT =====
export default useAuth;

// ===== EXPORTS DE TIPOS =====
export type { AuthContextValue, AuthResult, UserMetadata, AuthProviderProps };
