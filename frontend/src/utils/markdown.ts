import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// ===== INTERFACES =====
interface FrontmatterData {
  [key: string]: any;
  title?: string;
  date?: string;
  author?: string;
  categories?: string[];
  tags?: string[];
  excerpt?: string;
  featured?: boolean;
  published?: boolean;
  slug?: string;
  formattedDate?: string | null;
  readingTime?: string;
  readingMinutes?: number;
}

interface ParsedFrontmatter {
  data: FrontmatterData;
  content: string;
}

interface ReadingStats {
  text: string;
  minutes: number;
  words: number;
}

interface ParsedMarkdown {
  frontmatter: FrontmatterData;
  content: string;
  excerpt: string;
}

interface PostWithFrontmatter {
  frontmatter: FrontmatterData;
  [key: string]: any;
}

// ===== FUNÇÕES PRIVADAS =====

/**
 * Parser simples de frontmatter YAML
 * @param content - Conteúdo com frontmatter
 * @returns Objeto com data e content
 */
function parseFrontmatter(content: string): ParsedFrontmatter {
  const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
  const match = content.match(frontmatterRegex);

  if (!match) {
    return {
      data: {},
      content: content,
    };
  }

  const [, frontmatterStr, markdownContent] = match;
  const data: FrontmatterData = {};

  // Parse simples do YAML (apenas key: value)
  frontmatterStr.split('\n').forEach((line) => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const colonIndex = trimmed.indexOf(':');
      if (colonIndex > 0) {
        const key = trimmed.substring(0, colonIndex).trim();
        let value: any = trimmed.substring(colonIndex + 1).trim();

        // Remove aspas se existirem
        if (
          (value.startsWith('"') && value.endsWith('"')) ||
          (value.startsWith("'") && value.endsWith("'"))
        ) {
          value = value.slice(1, -1);
        }

        // Parse arrays simples [item1, item2]
        if (value.startsWith('[') && value.endsWith(']')) {
          value = value
            .slice(1, -1)
            .split(',')
            .map((item: string) => item.trim().replace(/['"]/g, ''))
            .filter((item: string) => item.length > 0);
        }

        // Parse booleans
        if (value === 'true') value = true;
        if (value === 'false') value = false;

        data[key] = value;
      }
    }
  });

  return {
    data,
    content: markdownContent,
  };
}

/**
 * Calcula o tempo de leitura estimado para um texto
 * @param text - Texto para calcular o tempo de leitura
 * @returns Objeto com informações de tempo de leitura
 */
function calculateReadingTime(text: string): ReadingStats {
  const wordsPerMinute = 200; // Velocidade média de leitura em português
  const words = text.trim().split(/\s+/).length;
  const minutes = Math.ceil(words / wordsPerMinute);

  return {
    text: `${minutes} min de leitura`,
    minutes: minutes,
    words: words,
  };
}

// ===== FUNÇÕES EXPORTADAS =====

/**
 * Processa conteúdo markdown com frontmatter
 * @param content - Conteúdo markdown bruto
 * @returns Conteúdo processado com metadados
 */
export function parseMarkdown(content: string): ParsedMarkdown {
  const { data, content: markdownContent } = parseFrontmatter(content);

  // Calcula tempo de leitura
  const readingStats = calculateReadingTime(markdownContent);

  // Formata data se existir
  const formattedDate = data.date
    ? format(new Date(data.date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })
    : null;

  return {
    frontmatter: {
      ...data,
      formattedDate,
      readingTime: readingStats.text,
      readingMinutes: Math.ceil(readingStats.minutes),
    },
    content: markdownContent,
    excerpt: data.excerpt || generateExcerpt(markdownContent),
  };
}

/**
 * Gera um resumo a partir do conteúdo markdown
 * @param content - Conteúdo markdown
 * @param maxLength - Comprimento máximo do resumo
 * @returns Resumo gerado
 */
export function generateExcerpt(content: string, maxLength: number = 160): string {
  // Remove sintaxe markdown e obtém texto simples
  const plainText = content
    .replace(/#{1,6}\s+/g, '') // Remove cabeçalhos
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove negrito
    .replace(/\*(.*?)\*/g, '$1') // Remove itálico
    .replace(/`(.*?)`/g, '$1') // Remove código inline
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .replace(/!\[.*?\]\(.*?\)/g, '') // Remove imagens
    .replace(/\n+/g, ' ') // Substitui quebras de linha por espaços
    .trim();

  if (plainText.length <= maxLength) {
    return plainText;
  }

  // Encontra a última palavra completa dentro do limite
  const truncated = plainText.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');

  return lastSpaceIndex > 0 ? truncated.substring(0, lastSpaceIndex) + '...' : truncated + '...';
}

/**
 * Cria um slug a partir do título
 * @param title - Título do post
 * @returns Slug amigável para URL
 */
export function createSlug(title: string): string {
  return title
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
    .replace(/\s+/g, '-') // Substitui espaços por hífens
    .replace(/-+/g, '-') // Substitui múltiplos hífens por um único
    .replace(/^-+|-+$/g, ''); // Remove hífens do início/fim
}

/**
 * Ordena posts por data (mais recente primeiro)
 * @param posts - Array de posts
 * @returns Posts ordenados
 */
export function sortPostsByDate<T extends PostWithFrontmatter>(posts: T[]): T[] {
  return posts.sort((a, b) => {
    const dateA = new Date(a.frontmatter.date || 0);
    const dateB = new Date(b.frontmatter.date || 0);
    return dateB.getTime() - dateA.getTime();
  });
}

/**
 * Filtra posts por categoria
 * @param posts - Array de posts
 * @param category - Categoria para filtrar
 * @returns Posts filtrados
 */
export function filterPostsByCategory<T extends PostWithFrontmatter>(
  posts: T[],
  category: string
): T[] {
  if (!category) return posts;

  return posts.filter((post) => {
    const categories = post.frontmatter.categories || [];
    return categories.includes(category);
  });
}

/**
 * Filtra posts por tag
 * @param posts - Array de posts
 * @param tag - Tag para filtrar
 * @returns Posts filtrados
 */
export function filterPostsByTag<T extends PostWithFrontmatter>(posts: T[], tag: string): T[] {
  if (!tag) return posts;

  return posts.filter((post) => {
    const tags = post.frontmatter.tags || [];
    return tags.includes(tag);
  });
}

/**
 * Obtém todas as categorias únicas dos posts
 * @param posts - Array de posts
 * @returns Categorias únicas
 */
export function getAllCategories<T extends PostWithFrontmatter>(posts: T[]): string[] {
  const categories = new Set<string>();

  posts.forEach((post) => {
    const postCategories = post.frontmatter.categories || [];
    postCategories.forEach((category) => categories.add(category));
  });

  return Array.from(categories).sort();
}

/**
 * Obtém todas as tags únicas dos posts
 * @param posts - Array de posts
 * @returns Tags únicas
 */
export function getAllTags<T extends PostWithFrontmatter>(posts: T[]): string[] {
  const tags = new Set<string>();

  posts.forEach((post) => {
    const postTags = post.frontmatter.tags || [];
    postTags.forEach((tag) => tags.add(tag));
  });

  return Array.from(tags).sort();
}

// ===== EXPORTS DE TIPOS =====
export type {
  FrontmatterData,
  ParsedFrontmatter,
  ReadingStats,
  ParsedMarkdown,
  PostWithFrontmatter,
};
