import { parseMarkdown, sortPostsByDate } from './markdown';
import type { Post, Project, Devlog } from '@/types';

// ===== INTERFACES LOCAIS =====
interface MockPost {
  slug: string;
  content: string;
}

interface MockProject {
  id: string;
  title: string;
  description: string;
  slug: string;
  status: 'active' | 'completed' | 'archived';
  technologies: string[];
  github_url?: string;
  demo_url?: string | null;
  image_url?: string;
  featured: boolean;
  created_at: string;
  updated_at: string;
}

interface MockDevlog {
  id: string;
  title: string;
  content: string;
  project_id: string;
  author_id: string;
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
  project?: Project;
}

// ===== MOCK DATA =====
const MOCK_POSTS: MockPost[] = [
  {
    slug: 'primeiro-post-blueprint',
    content: `---
title: "Bem-vindo ao Blueprint"
date: "2024-01-15"
author: "Dev Blueprint"
categories: ["meta", "anúncios"]
tags: ["blueprint", "blog", "início"]
excerpt: "O primeiro post do Blueprint está aqui! Conheça a visão por trás deste projeto e o que esperar nos próximos artigos."
featured: true
---

# Bem-vindo ao Blueprint

Este é o primeiro post do **Blueprint**, meu laboratório digital onde documento a jornada de desenvolvimento, compartilho conhecimento e construo no público.

## O que é o Blueprint?

O Blueprint não é apenas mais um blog de tecnologia. É:

- 🧪 **Laboratório**: Onde experimento com novas tecnologias
- 📚 **Documentação**: Registro público do meu aprendizado
- 🚀 **Portfólio**: Vitrine dos projetos que construo
- 💭 **Manifesto**: Minha visão sobre desenvolvimento e tecnologia

## O que esperar?

Nos próximos posts, você encontrará:

### Tutoriais Técnicos
- Guias práticos de React, Node.js, Python
- Arquitetura de software e boas práticas
- DevOps e deployment

### Projetos e Experimentos
- APIs que construo
- Bots e automações
- SaaS e ferramentas

### Reflexões
- Carreira em tech
- Tendências da indústria
- Lições aprendidas

## Por que "Blueprint"?

> "O mapa. A planta. O rascunho da vida dev."

Um blueprint é o plano, o esboço inicial de algo que será construído. Este blog é exatamente isso: o rascunho público da minha evolução como desenvolvedor.

Aqui você verá não apenas os sucessos, mas também os erros, as iterações e o processo completo de construção.

## Vamos construir juntos?

Este é apenas o começo. Nos próximos posts, vamos mergulhar fundo em código, arquitetura e muito mais.

Acompanhe a jornada! 🚀`,
  },
  {
    slug: 'configurando-react-vite-2024',
    content: `---
title: "Configurando React com Vite em 2024"
date: "2024-01-20"
author: "Dev Blueprint"
categories: ["tutoriais", "react"]
tags: ["react", "vite", "javascript", "frontend"]
excerpt: "Guia completo para configurar um projeto React moderno usando Vite, incluindo Tailwind CSS, ESLint e as melhores práticas de 2024."
featured: false
---

# Configurando React com Vite em 2024

O **Vite** revolucionou o desenvolvimento frontend com sua velocidade e simplicidade. Neste tutorial, vamos configurar um projeto React moderno do zero.

## Por que Vite?

- ⚡ **Extremamente rápido**: Hot reload instantâneo
- 📦 **Bundle otimizado**: Usa Rollup para produção
- 🔧 **Zero config**: Funciona out-of-the-box
- 🎯 **ESM nativo**: Aproveita módulos ES nativos

## Passo a Passo

### 1. Criando o projeto

\`\`\`bash
npm create vite@latest meu-projeto -- --template react
cd meu-projeto
npm install
\`\`\`

### 2. Configurando Tailwind CSS

\`\`\`bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
\`\`\`

### 3. Configurando o Tailwind

\`\`\`javascript
// tailwind.config.js
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
\`\`\`

### 4. Adicionando ao CSS

\`\`\`css
/* src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;
\`\`\`

## Estrutura Recomendada

\`\`\`
src/
├── components/
│   ├── ui/
│   └── layout/
├── pages/
├── hooks/
├── utils/
└── styles/
\`\`\`

## Próximos Passos

No próximo post, vamos adicionar:
- React Router
- Estado global com Zustand
- Testes com Vitest

Até lá! 🚀`,
  },
  {
    slug: 'construindo-api-nodejs-express',
    content: `---
title: "Construindo uma API REST com Node.js e Express"
date: "2024-01-25"
author: "Dev Blueprint"
categories: ["tutoriais", "backend"]
tags: ["nodejs", "express", "api", "backend"]
excerpt: "Tutorial completo para criar uma API REST robusta usando Node.js, Express, e as melhores práticas de desenvolvimento backend."
featured: true
---

# Construindo uma API REST com Node.js e Express

Vamos construir uma API REST completa do zero, seguindo as melhores práticas de desenvolvimento backend.

## Configuração Inicial

### 1. Setup do projeto

\`\`\`bash
mkdir minha-api
cd minha-api
npm init -y
npm install express cors helmet morgan dotenv
npm install -D nodemon
\`\`\`

### 2. Estrutura básica

\`\`\`javascript
// server.js
const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const morgan = require('morgan')

const app = express()
const PORT = process.env.PORT || 3000

// Middlewares
app.use(helmet())
app.use(cors())
app.use(morgan('combined'))
app.use(express.json())

// Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() })
})

app.listen(PORT, () => {
  console.log(\`🚀 Server running on port \${PORT}\`)
})
\`\`\`

## Implementando CRUD

### Modelo de dados

\`\`\`javascript
// models/User.js
class User {
  constructor(id, name, email) {
    this.id = id
    this.name = name
    this.email = email
    this.createdAt = new Date()
  }
}

module.exports = User
\`\`\`

### Controller

\`\`\`javascript
// controllers/userController.js
const User = require('../models/User')

let users = []
let nextId = 1

const userController = {
  // GET /api/users
  getAllUsers: (req, res) => {
    res.json(users)
  },

  // GET /api/users/:id
  getUserById: (req, res) => {
    const user = users.find(u => u.id === parseInt(req.params.id))
    if (!user) {
      return res.status(404).json({ error: 'User not found' })
    }
    res.json(user)
  },

  // POST /api/users
  createUser: (req, res) => {
    const { name, email } = req.body

    if (!name || !email) {
      return res.status(400).json({ error: 'Name and email are required' })
    }

    const user = new User(nextId++, name, email)
    users.push(user)

    res.status(201).json(user)
  },

  // PUT /api/users/:id
  updateUser: (req, res) => {
    const userIndex = users.findIndex(u => u.id === parseInt(req.params.id))

    if (userIndex === -1) {
      return res.status(404).json({ error: 'User not found' })
    }

    const { name, email } = req.body
    users[userIndex] = { ...users[userIndex], name, email }

    res.json(users[userIndex])
  },

  // DELETE /api/users/:id
  deleteUser: (req, res) => {
    const userIndex = users.findIndex(u => u.id === parseInt(req.params.id))

    if (userIndex === -1) {
      return res.status(404).json({ error: 'User not found' })
    }

    users.splice(userIndex, 1)
    res.status(204).send()
  }
}

module.exports = userController
\`\`\`

## Testando a API

Use o Postman ou curl para testar:

\`\`\`bash
# Criar usuário
curl -X POST http://localhost:3000/api/users \\
  -H "Content-Type: application/json" \\
  -d '{"name": "João", "email": "<EMAIL>"}'

# Listar usuários
curl http://localhost:3000/api/users
\`\`\`

## Próximos Passos

No próximo tutorial:
- Integração com banco de dados
- Autenticação JWT
- Validação com Joi
- Testes automatizados

Continue acompanhando! 🚀`,
  },
];

const MOCK_PROJECTS: MockProject[] = [
  {
    id: 'api-weather',
    title: 'Weather API',
    description: 'API REST para consulta de dados meteorológicos com cache Redis e rate limiting.',
    slug: 'api-weather',
    technologies: ['Node.js', 'Express', 'Redis', 'PostgreSQL'],
    status: 'completed',
    featured: true,
    github_url: 'https://github.com/user/weather-api',
    demo_url: 'https://weather-api.example.com',
    image_url: '/projects/weather-api.jpg',
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z',
  },
  {
    id: 'task-manager',
    title: 'Task Manager SaaS',
    description:
      'Aplicação completa de gerenciamento de tarefas com autenticação, colaboração em tempo real.',
    slug: 'task-manager',
    technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB'],
    status: 'active',
    featured: true,
    github_url: 'https://github.com/user/task-manager',
    demo_url: null,
    image_url: '/projects/task-manager.jpg',
    created_at: '2024-01-05T00:00:00Z',
    updated_at: '2024-01-05T00:00:00Z',
  },
  {
    id: 'crypto-bot',
    title: 'Crypto Trading Bot',
    description: 'Bot automatizado para trading de criptomoedas com estratégias configuráveis.',
    slug: 'crypto-bot',
    technologies: ['Python', 'FastAPI', 'Celery', 'Docker'],
    status: 'completed',
    featured: false,
    github_url: 'https://github.com/user/crypto-bot',
    demo_url: null,
    image_url: '/projects/crypto-bot.jpg',
    created_at: '2023-12-20T00:00:00Z',
    updated_at: '2023-12-20T00:00:00Z',
  },
];

// Mock data para devlogs
const MOCK_DEVLOGS: MockDevlog[] = [
  {
    id: 'devlog-001',
    title: 'Início do Blueprint Blog',
    content: `
## 🚀 Início do Projeto

Hoje começamos o desenvolvimento do **Blueprint Blog**, um blog pessoal moderno com tema cyberpunk.

### ✅ O que foi feito:
- Setup inicial com Vite + React
- Configuração do Tailwind CSS
- Estrutura de pastas organizada
- Primeiros componentes base

### 🎯 Próximos passos:
- Implementar sistema de navegação
- Criar páginas principais
- Definir tema visual cyberpunk
    `,
    project_id: 'blueprint-blog',
    author_id: 'user-001',
    status: 'published',
    created_at: '2025-05-25T00:00:00Z',
    updated_at: '2025-05-25T00:00:00Z',
  },
  {
    id: 'devlog-002',
    title: 'Sistema de Navegação Implementado',
    content: `
## 🎨 Sistema de Navegação

Implementação completa do header e navegação do site.

### ✅ Features implementadas:
- Header fixo com backdrop blur
- Menu mobile com animações
- Logo com efeito cyberpunk
- Links ativos com indicadores neon

### 🔧 Tecnologias:
- React Router para navegação
- Framer Motion para animações
- Lucide React para ícones
- Tailwind para styling

### 💡 Aprendizados:
- Como fazer backdrop blur responsivo
- Animações suaves com Framer Motion
- Estados de hover cyberpunk
    `,
    project_id: 'blueprint-blog',
    author_id: 'user-001',
    status: 'published',
    created_at: '2025-05-26T00:00:00Z',
    updated_at: '2025-05-26T00:00:00Z',
  },
];

// ===== FUNÇÕES EXPORTADAS =====

/**
 * Obtém todos os posts do blog
 * @returns Array de posts processados
 */
export async function getAllPosts(): Promise<Post[]> {
  try {
    const posts = MOCK_POSTS.map((post) => {
      const parsed = parseMarkdown(post.content) as any;
      return {
        id: post.slug,
        slug: post.slug,
        title: parsed.frontmatter?.title || 'Sem título',
        content: parsed.content || '',
        status: 'published' as const,
        published_at: parsed.frontmatter?.date || new Date().toISOString(),
        created_at: parsed.frontmatter?.date || new Date().toISOString(),
        updated_at: parsed.frontmatter?.date || new Date().toISOString(),
        author_id: 'user-001',
        language: 'pt' as const,
        views: 0,
        frontmatter: parsed.frontmatter,
      } as Post & { frontmatter: any };
    });

    return sortPostsByDate(posts).map((post) => {
      const { frontmatter, ...postData } = post;
      return postData as Post;
    });
  } catch (error) {
    console.error('Erro ao buscar posts:', error);
    return [];
  }
}

/**
 * Obtém um post específico pelo slug
 * @param slug - Slug do post
 * @returns Post processado ou null
 */
export async function getPostBySlug(slug: string): Promise<Post | null> {
  try {
    const post = MOCK_POSTS.find((p) => p.slug === slug);
    if (!post) return null;

    const parsed = parseMarkdown(post.content) as any;
    return {
      id: post.slug,
      slug: post.slug,
      title: parsed.frontmatter?.title || 'Sem título',
      content: parsed.content || '',
      status: 'published' as const,
      published_at: parsed.frontmatter?.date || new Date().toISOString(),
      created_at: parsed.frontmatter?.date || new Date().toISOString(),
      updated_at: parsed.frontmatter?.date || new Date().toISOString(),
      author_id: 'user-001',
      language: 'pt' as const,
      views: 0,
    } as Post;
  } catch (error) {
    console.error('Erro ao buscar post:', error);
    return null;
  }
}

/**
 * Obtém posts em destaque
 * @param limit - Número de posts para retornar
 * @returns Array de posts em destaque
 */
export async function getFeaturedPosts(limit: number = 3): Promise<Post[]> {
  try {
    const allPosts = await getAllPosts();
    return allPosts.filter((post) => (post as any).frontmatter?.featured).slice(0, limit);
  } catch (error) {
    console.error('Erro ao buscar posts em destaque:', error);
    return [];
  }
}

/**
 * Obtém todos os projetos
 * @returns Array de projetos
 */
export async function getAllProjects(): Promise<Project[]> {
  try {
    return MOCK_PROJECTS.map((project) => ({
      ...project,
      demo_url: project.demo_url || undefined,
    })).sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } catch (error) {
    console.error('Erro ao buscar projetos:', error);
    return [];
  }
}

/**
 * Obtém projetos em destaque
 * @param limit - Número de projetos para retornar
 * @returns Array de projetos em destaque
 */
export async function getFeaturedProjects(limit: number = 3): Promise<Project[]> {
  try {
    const allProjects = await getAllProjects();
    return allProjects.filter((project) => project.featured).slice(0, limit);
  } catch (error) {
    console.error('Erro ao buscar projetos em destaque:', error);
    return [];
  }
}

/**
 * Obtém todos os devlogs
 * @returns Array de devlogs
 */
export async function getAllDevlogs(): Promise<Devlog[]> {
  try {
    return MOCK_DEVLOGS.sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  } catch (error) {
    console.error('Erro ao buscar devlogs:', error);
    return [];
  }
}

/**
 * Obtém um devlog específico pelo ID
 * @param id - ID do devlog
 * @returns Devlog ou null
 */
export async function getDevlogById(id: string): Promise<Devlog | null> {
  try {
    const devlog = MOCK_DEVLOGS.find((d) => d.id === id);
    return devlog || null;
  } catch (error) {
    console.error('Erro ao buscar devlog:', error);
    return null;
  }
}

/**
 * Obtém devlogs recentes
 * @param limit - Número de devlogs para retornar
 * @returns Array de devlogs recentes
 */
export async function getRecentDevlogs(limit: number = 5): Promise<Devlog[]> {
  try {
    const allDevlogs = await getAllDevlogs();
    return allDevlogs.slice(0, limit);
  } catch (error) {
    console.error('Erro ao buscar devlogs recentes:', error);
    return [];
  }
}

/**
 * Obtém devlogs por projeto
 * @param projectId - ID do projeto
 * @returns Array de devlogs do projeto
 */
export async function getDevlogsByProject(projectId: string): Promise<Devlog[]> {
  try {
    const allDevlogs = await getAllDevlogs();
    return allDevlogs.filter((devlog) => devlog.project_id === projectId);
  } catch (error) {
    console.error('Erro ao buscar devlogs do projeto:', error);
    return [];
  }
}
