import { Suspense, lazy } from 'react';
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/layout/Layout';
import LoadingSpinner from './components/ui/LoadingSpinner';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './hooks/useAuth';

// Lazy loading das páginas para melhor performance
const Home = lazy(() => import('./pages/Home'));
const Blog = lazy(() => import('./pages/Blog'));
const Post = lazy(() => import('./pages/Post'));
const Projects = lazy(() => import('./pages/Projects'));
const Devlog = lazy(() => import('./pages/Devlog'));
const About = lazy(() => import('./pages/About'));
const Contact = lazy(() => import('./pages/Contact'));
const Themes = lazy(() => import('./pages/Themes'));

// Admin e Auth components
const Login = lazy(() => import('./components/auth/Login'));
const PostsList = lazy(() => import('./components/admin/PostsList'));
const PostEditor = lazy(() => import('./components/admin/PostEditor'));

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              {/* Rotas públicas com layout */}
              <Route
                path="/"
                element={
                  <Layout>
                    <Home />
                  </Layout>
                }
              />
              <Route
                path="/blog"
                element={
                  <Layout>
                    <Blog />
                  </Layout>
                }
              />
              <Route
                path="/blog/:slug"
                element={
                  <Layout>
                    <Post />
                  </Layout>
                }
              />
              <Route
                path="/projects"
                element={
                  <Layout>
                    <Projects />
                  </Layout>
                }
              />
              <Route
                path="/devlog"
                element={
                  <Layout>
                    <Devlog />
                  </Layout>
                }
              />
              <Route
                path="/about"
                element={
                  <Layout>
                    <About />
                  </Layout>
                }
              />
              <Route
                path="/contact"
                element={
                  <Layout>
                    <Contact />
                  </Layout>
                }
              />
              <Route
                path="/themes"
                element={
                  <Layout>
                    <Themes />
                  </Layout>
                }
              />

              {/* Rotas de autenticação (sem layout) */}
              <Route path="/login" element={<Login />} />

              {/* Rotas de admin (sem layout) */}
              <Route path="/admin" element={<PostsList />} />
              <Route path="/admin/posts" element={<PostsList />} />
              <Route path="/admin/posts/new" element={<PostEditor />} />
              <Route path="/admin/posts/edit/:slug" element={<PostEditor />} />
            </Routes>
          </Suspense>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'var(--color-card)',
                color: 'var(--color-text)',
                border: '1px solid var(--color-border)',
              },
              success: {
                iconTheme: {
                  primary: '#10b981',
                  secondary: 'white',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: 'white',
                },
              },
            }}
          />
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
