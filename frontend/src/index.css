@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Dark theme colors */
  --color-dark-bg: #0a0a0a;
  --color-dark-card: #111111;
  --color-dark-border: #222222;
  --color-dark-text: #ffffff;
  --color-dark-text-secondary: #a1a1aa;

  /* Light theme colors - mais harmoniosos com dark */
  --color-light-bg: #f1f3f4;
  --color-light-card: #fefefe;
  --color-light-border: #e5e7eb;
  --color-light-text: #1f2937;
  --color-light-text-secondary: #6b7280;

  /* Neon colors para dark mode */
  --color-neon-green: #00ff88;
  --color-neon-blue: #00d4ff;
  --color-cyber-purple: #8b5cf6;

  /* Light mode accent colors - mais vibrantes para consistência */
  --color-light-accent-blue: #2563eb;
  --color-light-accent-green: #059669;
  --color-light-accent-purple: #7c3aed;

  /* RGB values for opacity calculations */
  --color-neon-green-rgb: 0, 255, 136;
  --color-neon-blue-rgb: 0, 212, 255;
  --color-cyber-purple-rgb: 139, 92, 246;
  --color-light-accent-blue-rgb: 37, 99, 235;
  --color-light-accent-green-rgb: 5, 150, 105;
  --color-light-accent-purple-rgb: 124, 58, 237;

  /* Adaptive colors - default to dark */
  --color-bg: var(--color-dark-bg);
  --color-card: var(--color-dark-card);
  --color-border: var(--color-dark-border);
  --color-text: var(--color-dark-text);
  --color-text-secondary: var(--color-dark-text-secondary);
  --color-accent-blue: var(--color-neon-blue);
  --color-accent-green: var(--color-neon-green);
  --color-accent-purple: var(--color-cyber-purple);
  --color-accent-blue-rgb: var(--color-neon-blue-rgb);
  --color-accent-green-rgb: var(--color-neon-green-rgb);
  --color-accent-purple-rgb: var(--color-cyber-purple-rgb);
}

/* Light theme overrides */
.light {
  --color-bg: var(--color-light-bg);
  --color-card: var(--color-light-card);
  --color-border: var(--color-light-border);
  --color-text: var(--color-light-text);
  --color-text-secondary: var(--color-light-text-secondary);
  --color-accent-blue: var(--color-light-accent-blue);
  --color-accent-green: var(--color-light-accent-green);
  --color-accent-purple: var(--color-light-accent-purple);
  --color-accent-blue-rgb: var(--color-light-accent-blue-rgb);
  --color-accent-green-rgb: var(--color-light-accent-green-rgb);
  --color-accent-purple-rgb: var(--color-light-accent-purple-rgb);
}

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    @apply min-h-screen m-0 font-sans antialiased;
    background-color: var(--color-bg);
    color: var(--color-text);
  }

  /* Dark theme background pattern */
  .dark body {
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(0, 245, 255, 0.15) 1px,
      transparent 0
    );
    background-size: 20px 20px;
  }

  /* Light theme background pattern - mais consistente com dark */
  .light body {
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(37, 99, 235, 0.08) 1px,
      transparent 0
    );
    background-size: 20px 20px;
  }

  html {
    color-scheme: dark light;
  }

  .dark html {
    color-scheme: dark;
  }

  .light html {
    color-scheme: light;
  }
}

@layer components {
  .btn-primary {
    @apply px-6 py-3 font-medium transition-all duration-300 rounded-lg;
    background-color: var(--color-accent-blue);
    color: var(--color-bg);
    min-height: 2.75rem; /* h-11 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-primary:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  .dark .btn-primary:hover {
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.25);
  }

  .light .btn-primary:hover {
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
  }

  .btn-secondary {
    @apply px-6 py-3 font-medium transition-all duration-300 rounded-lg;
    border: 1px solid var(--color-accent-blue);
    color: var(--color-accent-blue);
    background-color: transparent;
    min-height: 2.75rem; /* h-11 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-secondary:hover {
    background-color: var(--color-accent-blue);
    color: var(--color-bg);
    transform: translateY(-1px);
  }

  .dark .btn-secondary:hover {
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.25);
  }

  .light .btn-secondary:hover {
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
  }

  /* Botão tamanho pequeno */
  .btn-sm {
    @apply px-4 py-2 text-sm;
    min-height: 2.25rem; /* h-9 */
  }

  .card {
    @apply p-6 rounded-lg;
    background-color: var(--color-card);
    border: 1px solid var(--color-border);
    /* Removendo transition automática que causa animação no load */
    transition: transform 0.2s ease, box-shadow 0.2s ease,
      border-color 0.2s ease;
  }

  /* Sombra padrão para light mode */
  .light .card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .card:hover {
    transform: translateY(-2px);
  }

  .dark .card:hover {
    border-color: rgba(0, 212, 255, 0.5);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.1);
  }

  .light .card:hover {
    border-color: rgba(37, 99, 235, 0.5);
    box-shadow: 0 12px 30px rgba(37, 99, 235, 0.15);
  }

  .text-gradient {
    @apply text-transparent bg-gradient-to-r bg-clip-text;
  }

  .dark .text-gradient {
    @apply from-neon-blue to-neon-green;
  }

  .light .text-gradient {
    @apply from-blue-600 to-purple-600;
  }

  .glow-text {
    text-shadow: 0 0 10px currentColor;
  }

  .light .glow-text {
    text-shadow: none;
  }

  /* Animação para o ícone */
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 10px currentColor;
    }
    to {
      box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    }
  }

  .light .animate-glow {
    animation: none; /* Remove animação no tema light */
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Adaptive background colors */
  .bg-adaptive {
    background-color: var(--color-bg);
  }

  .bg-adaptive-card {
    background-color: var(--color-card);
  }

  /* Additional missing classes */
  .text-adaptive {
    color: var(--color-text);
  }

  .hover\:bg-adaptive-card:hover {
    background-color: var(--color-card);
  }

  .hover\:border-adaptive-blue\/50:hover {
    border-color: rgba(var(--color-accent-blue-rgb), 0.5);
  }

  .hover\:shadow-lg:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .dark .hover\:shadow-lg:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 212, 255, 0.1), 0 4px 6px -2px rgba(0, 212, 255, 0.05);
  }

  /* Adaptive text colors */

  .text-adaptive-secondary {
    color: var(--color-text-secondary);
  }

  /* Adaptive border colors */
  .border-adaptive {
    border-color: var(--color-border);
  }

  .border-adaptive-border {
    border-color: var(--color-border);
  }

  /* Adaptive accent colors */
  .text-adaptive-blue {
    color: var(--color-accent-blue);
  }

  .text-adaptive-green {
    color: var(--color-accent-green);
  }

  .text-adaptive-purple {
    color: var(--color-accent-purple);
  }

  .border-adaptive-blue {
    border-color: var(--color-accent-blue);
  }

  .border-adaptive-green {
    border-color: var(--color-accent-green);
  }

  .bg-adaptive-blue {
    background-color: var(--color-accent-blue);
  }

  .bg-adaptive-green {
    background-color: var(--color-accent-green);
  }

  .bg-adaptive-purple {
    background-color: var(--color-accent-purple);
  }

  .border-adaptive-purple {
    border-color: var(--color-accent-purple);
  }

  /* Hover states */
  .hover-adaptive-blue:hover {
    color: var(--color-accent-blue);
  }

  .hover-adaptive-green:hover {
    color: var(--color-accent-green);
  }

  .hover-adaptive-purple:hover {
    color: var(--color-accent-purple);
  }

  /* Focus states */
  .focus-adaptive-blue:focus {
    border-color: var(--color-accent-blue);
    outline: none;
  }

  .focus-adaptive-green:focus {
    border-color: var(--color-accent-green);
    outline: none;
  }

  .focus-adaptive-purple:focus {
    border-color: var(--color-accent-purple);
    outline: none;
  }

  /* Placeholder colors */
  .placeholder-adaptive-secondary::placeholder {
    color: var(--color-text-secondary);
  }

  /* Background with opacity */
  .bg-adaptive-green\/10 {
    background-color: rgba(var(--color-accent-green-rgb), 0.1);
  }

  .bg-adaptive-blue\/10 {
    background-color: rgba(var(--color-accent-blue-rgb), 0.1);
  }

  .bg-adaptive-blue\/5 {
    background-color: rgba(var(--color-accent-blue-rgb), 0.05);
  }

  .border-adaptive-green\/20 {
    border-color: rgba(var(--color-accent-green-rgb), 0.2);
  }

  .border-adaptive-blue\/20 {
    border-color: rgba(var(--color-accent-blue-rgb), 0.2);
  }

  .border-adaptive-blue\/30 {
    border-color: rgba(var(--color-accent-blue-rgb), 0.3);
  }

  .bg-adaptive-purple\/10 {
    background-color: rgba(var(--color-accent-purple-rgb), 0.1);
  }

  .border-adaptive-purple\/20 {
    border-color: rgba(var(--color-accent-purple-rgb), 0.2);
  }

  /* Hover border colors */
  .hover\:border-adaptive-blue:hover {
    border-color: var(--color-accent-blue);
  }

  .hover\:border-adaptive-green:hover {
    border-color: var(--color-accent-green);
  }

  .hover\:border-adaptive-purple:hover {
    border-color: var(--color-accent-purple);
  }

  /* Hover background colors */
  .hover\:bg-adaptive-blue\/5:hover {
    background-color: rgba(var(--color-accent-blue-rgb), 0.05);
  }

  .hover\:bg-adaptive-blue\/10:hover {
    background-color: rgba(var(--color-accent-blue-rgb), 0.1);
  }

  /* Additional color utilities */
  .hover\:text-adaptive-blue:hover {
    color: var(--color-accent-blue);
  }

  .hover\:text-adaptive-green:hover {
    color: var(--color-accent-green);
  }

  .hover\:text-adaptive-purple:hover {
    color: var(--color-accent-purple);
  }

  /* Ring colors for focus states */
  .focus\:ring-adaptive-blue:focus {
    --tw-ring-color: var(--color-accent-blue);
  }

  .focus\:ring-adaptive-green:focus {
    --tw-ring-color: var(--color-accent-green);
  }

  .focus\:ring-adaptive-purple:focus {
    --tw-ring-color: var(--color-accent-purple);
  }

  .focus\:ring-offset-adaptive:focus {
    --tw-ring-offset-color: var(--color-bg);
  }

  /* Gradient backgrounds */
  .from-adaptive-purple {
    --tw-gradient-from: var(--color-accent-purple);
    --tw-gradient-to: rgb(var(--color-accent-purple-rgb) / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-adaptive-blue {
    --tw-gradient-to: var(--color-accent-blue);
  }

  .from-adaptive-blue {
    --tw-gradient-from: var(--color-accent-blue);
    --tw-gradient-to: rgb(var(--color-accent-blue-rgb) / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-adaptive-green {
    --tw-gradient-to: var(--color-accent-green);
  }
}
