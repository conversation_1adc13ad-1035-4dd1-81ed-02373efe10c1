# Frontend Dockerfile - Apenas Build (sem nginx)
FROM node:20-alpine as builder

WORKDIR /app

# Instalar dependências
COPY package*.json ./
RUN rm -rf node_modules package-lock.json && npm install --force

# Copiar código fonte
COPY . .

# Configurar variáveis de ambiente para BUILD TIME
ARG VITE_API_URL=http://localhost/api
ENV NODE_ENV=production
ENV VITE_API_URL=$VITE_API_URL

# Build da aplicação
RUN npm run build

# Estágio de produção
FROM nginx:alpine

# Copiar configuração do nginx
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copiar arquivos buildados
COPY --from=builder /app/dist /usr/share/nginx/html

# Expor porta
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost/ || exit 1

# Comando para manter o container rodando
CMD ["nginx", "-g", "daemon off;"]