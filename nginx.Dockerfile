# Nginx Único - Serve Frontend + Proxy Backend
FROM node:20-alpine AS frontend-builder

WORKDIR /app

# Copy frontend package files
COPY frontend/package*.json ./

# Install dependencies
RUN rm -rf node_modules package-lock.json && npm install --force

# Copy frontend source
COPY frontend/ ./

# Build frontend
RUN npm run build

# Production nginx stage
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built frontend
COPY --from=frontend-builder /app/dist /usr/share/nginx/html

# Copy nginx config
COPY nginx.conf /etc/nginx/nginx.conf

# Create log directory
RUN mkdir -p /var/log/nginx

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

CMD ["nginx", "-g", "daemon off;"]
