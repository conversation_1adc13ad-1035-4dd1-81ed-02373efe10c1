-- Blueprint Blog - Schema do Banco de Dados
-- Execute este SQL no Supabase SQL Editor

-- 1. <PERSON><PERSON><PERSON><PERSON> necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 2. <PERSON>bel<PERSON> de Posts
CREATE TABLE IF NOT EXISTS posts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT,
  featured_image VARCHAR(500),
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  language VARCHAR(5) DEFAULT 'pt' CHECK (language IN ('pt', 'en')),
  tags TEXT[], -- Array de tags
  reading_time INTEGER, -- Tempo de leitura em minutos
  views INTEGER DEFAULT 0,
  likes INTEGER DEFAULT 0,
  author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE
);

-- 3. Tabela de Categorias
CREATE TABLE IF NOT EXISTS categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  color VARCHAR(7), -- Hex color
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Tabela de relacionamento Posts-Categorias (many-to-many)
CREATE TABLE IF NOT EXISTS post_categories (
  post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
  category_id UUID REFERENCES categories(id) ON DELETE CASCADE,
  PRIMARY KEY (post_id, category_id)
);

-- 5. Tabela de Projetos
CREATE TABLE IF NOT EXISTS projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  description TEXT NOT NULL,
  short_description VARCHAR(500),
  featured_image VARCHAR(500),
  demo_url VARCHAR(500),
  github_url VARCHAR(500),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived', 'in_progress')),
  tech_stack TEXT[], -- Array de tecnologias
  language VARCHAR(5) DEFAULT 'pt' CHECK (language IN ('pt', 'en')),
  views INTEGER DEFAULT 0,
  likes INTEGER DEFAULT 0,
  author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Tabela de Devlogs
CREATE TABLE IF NOT EXISTS devlogs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  type VARCHAR(20) DEFAULT 'update' CHECK (type IN ('update', 'feature', 'bugfix', 'release')),
  language VARCHAR(5) DEFAULT 'pt' CHECK (language IN ('pt', 'en')),
  author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Índices para performance
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
CREATE INDEX IF NOT EXISTS idx_posts_language ON posts(language);
CREATE INDEX IF NOT EXISTS idx_posts_published_at ON posts(published_at);
CREATE INDEX IF NOT EXISTS idx_posts_author ON posts(author_id);
CREATE INDEX IF NOT EXISTS idx_posts_slug ON posts(slug);

CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_language ON projects(language);
CREATE INDEX IF NOT EXISTS idx_projects_author ON projects(author_id);
CREATE INDEX IF NOT EXISTS idx_projects_slug ON projects(slug);

CREATE INDEX IF NOT EXISTS idx_devlogs_project ON devlogs(project_id);
CREATE INDEX IF NOT EXISTS idx_devlogs_type ON devlogs(type);

-- 8. Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 9. Triggers para updated_at (com verificação se já existem)
DROP TRIGGER IF EXISTS update_posts_updated_at ON posts;
CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_projects_updated_at ON projects;
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. RLS (Row Level Security) - Configuração básica
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE devlogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Políticas básicas (todos podem ler, apenas autenticados podem escrever)
CREATE POLICY "Posts são públicos para leitura" ON posts FOR SELECT USING (true);
CREATE POLICY "Apenas autenticados podem criar posts" ON posts FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
CREATE POLICY "Apenas autores podem editar seus posts" ON posts FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Apenas autores podem deletar seus posts" ON posts FOR DELETE USING (auth.uid() = author_id);

CREATE POLICY "Projetos são públicos para leitura" ON projects FOR SELECT USING (true);
CREATE POLICY "Apenas autenticados podem criar projetos" ON projects FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
CREATE POLICY "Apenas autores podem editar seus projetos" ON projects FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Apenas autores podem deletar seus projetos" ON projects FOR DELETE USING (auth.uid() = author_id);

CREATE POLICY "Devlogs são públicos para leitura" ON devlogs FOR SELECT USING (true);
CREATE POLICY "Apenas autenticados podem criar devlogs" ON devlogs FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
CREATE POLICY "Apenas autores podem editar seus devlogs" ON devlogs FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Apenas autores podem deletar seus devlogs" ON devlogs FOR DELETE USING (auth.uid() = author_id);

CREATE POLICY "Categorias são públicas para leitura" ON categories FOR SELECT USING (true);
CREATE POLICY "Apenas autenticados podem gerenciar categorias" ON categories FOR ALL USING (auth.uid() IS NOT NULL);

-- 11. Função para incrementar views
CREATE OR REPLACE FUNCTION increment_post_views(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE posts SET views = COALESCE(views, 0) + 1 WHERE id = post_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. Inserir categorias padrão
INSERT INTO categories (name, slug, description, color) VALUES
('Tecnologia', 'tecnologia', 'Posts sobre tecnologia e programação', '#3B82F6'),
('Tutorial', 'tutorial', 'Tutoriais e guias passo a passo', '#10B981'),
('Projeto', 'projeto', 'Documentação de projetos', '#8B5CF6'),
('Devlog', 'devlog', 'Logs de desenvolvimento', '#F59E0B'),
('Pessoal', 'pessoal', 'Posts pessoais e reflexões', '#EF4444')
ON CONFLICT (slug) DO NOTHING;
