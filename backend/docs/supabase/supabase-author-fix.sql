-- Fix para acessar dados do autor
-- Execute este SQL no Supabase SQL Editor

-- 1. Adicionar campo author_name na tabela posts
ALTER TABLE posts ADD COLUMN IF NOT EXISTS author_name VARCHAR(255);

-- 2. Função para extrair nome do usu<PERSON>rio
CREATE OR REPLACE FUNCTION get_user_display_name(user_id UUID)
RETURNS VARCHAR(255) AS $$
DECLARE
  user_data RECORD;
  display_name VARCHAR(255);
BEGIN
  SELECT
    email,
    raw_user_meta_data
  INTO user_data
  FROM auth.users
  WHERE id = user_id;

  IF user_data IS NULL THEN
    RETURN 'Autor Desconhecido';
  END IF;

  -- Tentar extrair nome do metadata
  display_name := user_data.raw_user_meta_data->>'full_name';

  IF display_name IS NULL OR display_name = '' THEN
    display_name := user_data.raw_user_meta_data->>'name';
  END IF;

  IF display_name IS NULL OR display_name = '' THEN
    display_name := split_part(user_data.email, '@', 1);
  END IF;

  RETURN COALESCE(display_name, 'Autor Desconhecido');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Atualizar posts existentes com author_name
UPDATE posts
SET author_name = get_user_display_name(author_id)
WHERE author_name IS NULL AND author_id IS NOT NULL;

-- 4. Trigger para atualizar author_name automaticamente
CREATE OR REPLACE FUNCTION update_post_author_name()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.author_id IS NOT NULL THEN
    NEW.author_name := get_user_display_name(NEW.author_id);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Criar trigger
DROP TRIGGER IF EXISTS trigger_update_post_author_name ON posts;
CREATE TRIGGER trigger_update_post_author_name
  BEFORE INSERT OR UPDATE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION update_post_author_name();
