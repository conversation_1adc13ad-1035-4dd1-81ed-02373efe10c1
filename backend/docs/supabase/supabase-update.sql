-- Blueprint Blog - Atualizações para Supabase
-- Execute apenas este SQL se você já executou o schema principal

-- 1. Função para incrementar views (se não existir)
CREATE OR REPLACE FUNCTION increment_post_views(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE posts SET views = COALESCE(views, 0) + 1 WHERE id = post_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Verificar se as políticas RLS existem e criar apenas se necessário
DO $$
BEGIN
    -- Verificar se a política de leitura de posts existe
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'posts' 
        AND policyname = 'Posts são públicos para leitura'
    ) THEN
        CREATE POLICY "Posts são públicos para leitura" ON posts FOR SELECT USING (true);
    END IF;

    -- Verificar se a política de criação de posts existe
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'posts' 
        AND policyname = 'Apenas autenticados podem criar posts'
    ) THEN
        CREATE POLICY "Apenas autenticados podem criar posts" ON posts FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
    END IF;

    -- Verificar se a política de edição de posts existe
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'posts' 
        AND policyname = 'Apenas autores podem editar seus posts'
    ) THEN
        CREATE POLICY "Apenas autores podem editar seus posts" ON posts FOR UPDATE USING (auth.uid() = author_id);
    END IF;

    -- Verificar se a política de deleção de posts existe
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'posts' 
        AND policyname = 'Apenas autores podem deletar seus posts'
    ) THEN
        CREATE POLICY "Apenas autores podem deletar seus posts" ON posts FOR DELETE USING (auth.uid() = author_id);
    END IF;
END $$;

-- 3. Inserir categorias padrão (apenas se não existirem)
INSERT INTO categories (name, slug, description, color) VALUES
('Tecnologia', 'tecnologia', 'Posts sobre tecnologia e programação', '#3B82F6'),
('Tutorial', 'tutorial', 'Tutoriais e guias passo a passo', '#10B981'),
('Projeto', 'projeto', 'Documentação de projetos', '#8B5CF6'),
('Devlog', 'devlog', 'Logs de desenvolvimento', '#F59E0B'),
('Pessoal', 'pessoal', 'Posts pessoais e reflexões', '#EF4444')
ON CONFLICT (slug) DO NOTHING;

-- 4. Verificar se RLS está habilitado
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE devlogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
