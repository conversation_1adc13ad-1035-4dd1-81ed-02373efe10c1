# Backend Dockerfile
FROM rust:1.82 AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Verify Cargo installation
RUN cargo --version && rustc --version

WORKDIR /app

# Copy Cargo files
COPY Cargo.toml ./
COPY .env ./.env

# Create dummy main.rs to cache dependencies
RUN mkdir src && echo "fn main() {}" > src/main.rs

# Build dependencies (cached layer) - this will generate Cargo.lock
RUN cargo build --release
RUN rm src/main.rs

# Copy source code and migrations
COPY src ./src
COPY .env ./.env

# Build the application
RUN touch src/main.rs && cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/target/release/blueprint-blog-backend /app/

# Create necessary directories
RUN mkdir -p uploads logs

# Expose port
EXPOSE 3001

# Run the binary
CMD ["./blueprint-blog-backend"]
