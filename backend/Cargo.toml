[package]
name = "blueprint-blog-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web Framework
axum = "0.6"
tokio = { version = "1.28", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.4", features = ["cors"] }

# Database
sqlx = { version = "0.6", features = ["postgres", "runtime-tokio-rustls", "chrono", "uuid", "migrate"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Authentication & Security
jsonwebtoken = "9.2"
bcrypt = "0.15"
uuid = { version = "1.6", features = ["v4", "serde"] }

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Validation
validator = { version = "0.16", features = ["derive"] }

# Utilities
lazy_static = "1.4"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-appender = "0.2"

# Config
dotenvy = "0.15"

# Utils (removed duplicates)

# Cache
redis = { version = "0.23", features = ["tokio-comp"], optional = true }

# Image processing (optional)
image = { version = "0.24", optional = true }

[features]
default = ["cache"]
cache = ["redis"]
images = ["image"]
