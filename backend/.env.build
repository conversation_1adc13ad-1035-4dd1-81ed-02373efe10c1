# ===========================================
# PRODUÇÃO LOCAL (.env.build)
# Para: docker-compose-prod.yml, simulação
# ===========================================

# Database - Supabase (Produção Local)
DATABASE_URL=postgresql://postgres.nbzovkwvrqlxqpdpkvql:<EMAIL>:6543/postgres

# Redis Cache (DESABILITADO temporariamente)
# REDIS_URL=redis://redis:6379

# JWT Secret (produção local)
JWT_SECRET=prod-local-jwt-secret-change-for-real-production

# Server Configuration
HOST=0.0.0.0
PORT=3001

# Environment
RUST_ENV=production

# Logging Configuration (Estruturado para produção)
RUST_LOG=info
LOG_LEVEL=info
LOG_CONSOLE=false
LOG_FILE=true
LOG_JSON=true
LOG_DIR=./logs
LOG_MAX_FILES=30
LOG_MAX_AGE_DAYS=30
