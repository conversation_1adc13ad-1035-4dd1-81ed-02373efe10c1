use axum::{
    routing::get,
    Router,
    Json,
    Extension,
    middleware::from_fn,
};
use serde_json::{json, Value};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;
use tracing::{info, error, warn};

mod database;
mod models;
mod logging;
mod routes;
mod auth;
mod middleware;
mod cache;
// mod services;
// mod utils;

use database::{create_pool, health_check, DatabasePool};
use logging::{LogConfig, init_logging};
use cache::CacheService;

#[tokio::main]
async fn main() {
    // Load environment variables first
    dotenvy::dotenv().expect("Failed to load .env file");

    // Initialize logging system
    let log_config = LogConfig {
        level: std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
        console_enabled: std::env::var("LOG_CONSOLE")
            .unwrap_or_else(|_| "true".to_string())
            .parse()
            .unwrap_or(true),
        file_enabled: std::env::var("LOG_FILE")
            .unwrap_or_else(|_| "true".to_string())
            .parse()
            .unwrap_or(true),
        json_enabled: std::env::var("LOG_JSON")
            .unwrap_or_else(|_| "true".to_string())
            .parse()
            .unwrap_or(true),
        log_dir: std::env::var("LOG_DIR").unwrap_or_else(|_| "./logs".to_string()),
        max_files: std::env::var("LOG_MAX_FILES")
            .unwrap_or_else(|_| "30".to_string())
            .parse()
            .unwrap_or(30),
    };

    if let Err(e) = init_logging(log_config) {
        eprintln!("❌ Failed to initialize logging: {}", e);
        std::process::exit(1);
    }

    // Get database URL
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://postgres.nbzovkwvrqlxqpdpkvql:<EMAIL>:6543/postgres".to_string());

    // Create database connection pool
    let pool = match create_pool(&database_url).await {
        Ok(pool) => pool,
        Err(e) => {
            error!("❌ Failed to create database pool: {}", e);
            std::process::exit(1);
        }
    };

    // 🚧 TEMPORÁRIO: Desabilitar Redis para desenvolvimento
    warn!("⚠️ Redis cache disabled for development");
    let cache = CacheService::new_disabled();

    // 🎯 SUPABASE: Não executar migrations - tabelas já existem no Supabase
    info!("🗄️ Using Supabase database - skipping local migrations");

    // Build our application with routes
    let app = Router::new()
        // Health checks
        .route("/health", get(app_health_check))
        .route("/api/health", get(app_health_check))
        .route("/api/db/health", get(db_health_check))
        .route("/api/logs/stats", get(log_stats))
        // API routes
        .merge(routes::create_routes())
        .layer(Extension(pool))
        .layer(Extension(cache))
        // 🔧 Middlewares para controle de CPU e recursos
        .layer(from_fn(middleware::request_timeout_middleware))
        .layer(from_fn(middleware::cpu_throttle_middleware))
        .layer(from_fn(middleware::rate_limit_middleware))
        .layer(from_fn(logging::middleware::request_logging))
        .layer(CorsLayer::permissive()); // TODO: Configure CORS properly

    // Run it with hyper on all interfaces for Docker networking
    let addr = SocketAddr::from(([0, 0, 0, 0], 3001));
    info!("🦀 Blueprint Blog Backend starting on {}", addr);

    axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await
        .unwrap();
}

async fn app_health_check() -> Json<Value> {
    Json(json!({
        "status": "ok",
        "service": "blueprint-blog-backend",
        "version": "0.1.0",
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}

async fn db_health_check(Extension(pool): Extension<DatabasePool>) -> Json<Value> {
    match health_check(&pool).await {
        Ok(_) => {
            info!("✅ Database health check passed");
            Json(json!({
                "status": "ok",
                "database": "connected",
                "timestamp": chrono::Utc::now().to_rfc3339()
            }))
        },
        Err(e) => {
            error!("❌ Database health check failed: {}", e);
            Json(json!({
                "status": "error",
                "database": "disconnected",
                "error": e.to_string(),
                "timestamp": chrono::Utc::now().to_rfc3339()
            }))
        }
    }
}

async fn log_stats() -> Json<Value> {
    let log_dir = std::env::var("LOG_DIR").unwrap_or_else(|_| "./logs".to_string());

    match logging::rotation::get_log_stats(&log_dir) {
        Ok(stats) => {
            info!("📊 Log stats requested - {} files, {:.2} MB", stats.file_count, stats.total_size_mb());
            Json(json!({
                "status": "ok",
                "log_directory": log_dir,
                "file_count": stats.file_count,
                "text_files": stats.text_files,
                "json_files": stats.json_files,
                "total_size_bytes": stats.total_size,
                "total_size_mb": stats.total_size_mb(),
                "timestamp": chrono::Utc::now().to_rfc3339()
            }))
        },
        Err(e) => {
            warn!("⚠️ Failed to get log stats: {}", e);
            Json(json!({
                "status": "error",
                "error": e.to_string(),
                "timestamp": chrono::Utc::now().to_rfc3339()
            }))
        }
    }
}
