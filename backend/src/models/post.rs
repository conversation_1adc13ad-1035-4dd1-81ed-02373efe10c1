use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use validator::Validate;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "post_status", rename_all = "lowercase")]
pub enum PostStatus {
    Draft,
    Published,
    Archived,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Post {
    pub id: Uuid,
    pub title: String,
    pub slug: String,
    pub excerpt: Option<String>,
    pub content: String,
    pub featured_image: Option<String>,
    pub author_id: Uuid,
    pub author_name: Option<String>,
    pub status: PostStatus,
    pub reading_time: Option<i32>,
    pub views: i32,
    pub likes: i32,
    pub tags: Vec<String>,
    pub meta_title: Option<String>,
    pub meta_description: Option<String>,
    pub published_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct PostSummary {
    pub id: Uuid,
    pub title: String,
    pub slug: String,
    pub excerpt: Option<String>,
    pub featured_image: Option<String>,
    pub author_name: Option<String>,
    pub status: PostStatus,
    pub reading_time: Option<i32>,
    pub views: i32,
    pub likes: i32,
    pub tags: Vec<String>,
    pub published_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreatePostRequest {
    #[validate(length(min = 1, max = 255))]
    pub title: String,
    #[validate(length(min = 1, max = 255))]
    pub slug: String,
    #[validate(length(max = 500))]
    pub excerpt: Option<String>,
    #[validate(length(min = 1))]
    pub content: String,
    pub featured_image: Option<String>,
    pub tags: Option<Vec<String>>,
    pub meta_title: Option<String>,
    pub meta_description: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdatePostRequest {
    #[validate(length(min = 1, max = 255))]
    pub title: Option<String>,
    #[validate(length(min = 1, max = 255))]
    pub slug: Option<String>,
    #[validate(length(max = 500))]
    pub excerpt: Option<String>,
    #[validate(length(min = 1))]
    pub content: Option<String>,
    pub featured_image: Option<String>,
    pub status: Option<PostStatus>,
    pub tags: Option<Vec<String>>,
    pub meta_title: Option<String>,
    pub meta_description: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct PostQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub status: Option<PostStatus>,
    pub author_id: Option<Uuid>,
    pub tag: Option<String>,
    pub search: Option<String>,
}

impl Post {
    pub fn to_summary(&self) -> PostSummary {
        PostSummary {
            id: self.id,
            title: self.title.clone(),
            slug: self.slug.clone(),
            excerpt: self.excerpt.clone(),
            featured_image: self.featured_image.clone(),
            author_name: self.author_name.clone(),
            status: self.status.clone(),
            reading_time: self.reading_time,
            views: self.views,
            likes: self.likes,
            tags: self.tags.clone(),
            published_at: self.published_at,
            created_at: self.created_at,
        }
    }

    pub fn calculate_reading_time(&self) -> i32 {
        // Average reading speed: 200 words per minute
        let word_count = self.content.split_whitespace().count();
        ((word_count as f32 / 200.0).ceil() as i32).max(1)
    }
}
