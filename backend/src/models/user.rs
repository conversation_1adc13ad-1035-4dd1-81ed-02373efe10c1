use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use validator::Validate;

// Representa o perfil do usuário (complementa auth.users do Supabase)
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct UserProfile {
    pub id: Uuid,
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    pub bio: Option<String>,
    pub is_admin: bool,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// Representa dados do auth.users do Supabase (apenas para referência)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SupabaseUser {
    pub id: Uuid,
    pub email: String,
    pub email_confirmed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PublicUserProfile {
    pub id: Uuid,
    pub email: Option<String>, // Vem do Supabase auth.users
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    pub bio: Option<String>,
    pub is_admin: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateUserProfileRequest {
    #[validate(length(min = 2, max = 255))]
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    #[validate(length(max = 1000))]
    pub bio: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateUserProfileRequest {
    #[validate(length(min = 2, max = 255))]
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    #[validate(length(max = 1000))]
    pub bio: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(email)]
    pub email: String,
    #[validate(length(min = 1))]
    pub password: String,
}

impl UserProfile {
    pub fn to_public(&self, email: Option<String>) -> PublicUserProfile {
        PublicUserProfile {
            id: self.id,
            email,
            full_name: self.full_name.clone(),
            avatar_url: self.avatar_url.clone(),
            bio: self.bio.clone(),
            is_admin: self.is_admin,
            created_at: self.created_at,
        }
    }

    pub fn display_name(&self) -> String {
        self.full_name
            .clone()
            .unwrap_or_else(|| "User".to_string())
    }
}
