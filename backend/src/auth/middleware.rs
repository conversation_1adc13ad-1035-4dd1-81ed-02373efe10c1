use axum::{
    http::{HeaderMap, StatusCode, Request},
    middleware::Next,
    response::Response,
    Extension,
    body::Body,
};
use serde_json::json;
use tracing::{info, warn, error};

use super::{AuthService, AuthUser, AuthError, AUTH_SERVICE};

// Middleware para verificar autenticação
pub async fn auth_middleware(
    headers: HeaderMap,
    mut request: Request<Body>,
    next: Next<Body>,
) -> Result<Response, StatusCode> {
    // Extrair token do header Authorization
    let auth_header = headers
        .get("authorization")
        .and_then(|header| header.to_str().ok());

    let token = match auth_header {
        Some(header) => match AUTH_SERVICE.extract_token_from_header(header) {
            Some(token) => token,
            None => {
                warn!("🔒 Invalid authorization header format");
                return Err(StatusCode::UNAUTHORIZED);
            }
        },
        None => {
            warn!("🔒 Missing authorization header");
            return Err(StatusCode::UNAUTHORIZED);
        }
    };

    // Verificar e decodificar token
    let claims = match AUTH_SERVICE.verify_token(token) {
        Ok(claims) => claims,
        Err(e) => {
            warn!("🔒 Token verification failed: {}", e);
            return Err(StatusCode::UNAUTHORIZED);
        }
    };

    // Criar AuthUser e adicionar ao request
    let auth_user = AuthUser {
        id: claims.sub.parse().map_err(|_| {
            error!("🔒 Invalid user ID in token");
            StatusCode::UNAUTHORIZED
        })?,
        email: claims.email,
        role: claims.role,
    };

    info!("🔓 User authenticated: {} ({})", auth_user.email, auth_user.role);

    // Adicionar user ao request extensions
    request.extensions_mut().insert(auth_user);

    // Continuar para o próximo middleware/handler
    Ok(next.run(request).await)
}

// Middleware para verificar se o usuário é admin
pub async fn admin_middleware(
    Extension(auth_user): Extension<AuthUser>,
    request: Request<Body>,
    next: Next<Body>,
) -> Result<Response, StatusCode> {
    if auth_user.role != "admin" {
        warn!("🚫 Access denied: user {} is not admin", auth_user.email);
        return Err(StatusCode::FORBIDDEN);
    }

    info!("👑 Admin access granted: {}", auth_user.email);
    Ok(next.run(request).await)
}

// Middleware opcional de autenticação (não falha se não houver token)
pub async fn optional_auth_middleware(
    headers: HeaderMap,
    mut request: Request<Body>,
    next: Next<Body>,
) -> Response {
    // Tentar extrair token
    if let Some(auth_header) = headers.get("authorization").and_then(|h| h.to_str().ok()) {
        if let Some(token) = AUTH_SERVICE.extract_token_from_header(auth_header) {
            if let Ok(claims) = AUTH_SERVICE.verify_token(token) {
                if let Ok(user_id) = claims.sub.parse() {
                    let auth_user = AuthUser {
                        id: user_id,
                        email: claims.email,
                        role: claims.role,
                    };
                    request.extensions_mut().insert(auth_user);
                }
            }
        }
    }

    next.run(request).await
}

// Helper para extrair usuário autenticado do request
pub fn get_auth_user(request: &Request<Body>) -> Option<&AuthUser> {
    request.extensions().get::<AuthUser>()
}

// Helper para verificar se usuário é admin
pub fn is_admin(auth_user: &AuthUser) -> bool {
    auth_user.role == "admin"
}

// Helper para verificar se usuário pode acessar recurso
pub fn can_access_user_resource(auth_user: &AuthUser, target_user_id: &str) -> bool {
    // Admin pode acessar qualquer recurso
    if is_admin(auth_user) {
        return true;
    }

    // Usuário pode acessar apenas seus próprios recursos
    auth_user.id.to_string() == target_user_id
}
