use std::env;
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Valida<PERSON>};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use bcrypt::{hash, verify, DEFAULT_COST};

pub mod middleware;

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // Subject (user ID)
    pub email: String,    // User email
    pub role: String,     // User role (admin, user)
    pub exp: i64,         // Expiration time
    pub iat: i64,         // Issued at
    pub jti: String,      // JWT ID (for token revocation)
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenPair {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: i64,
    pub token_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthUser {
    pub id: Uuid,
    pub email: String,
    pub role: String,
}

pub struct AuthService {
    encoding_key: Encoding<PERSON>ey,
    decoding_key: Decoding<PERSON><PERSON>,
    access_token_duration: Duration,
    refresh_token_duration: Duration,
}

impl AuthService {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let secret = env::var("JWT_SECRET")
            .unwrap_or_else(|_| "your-super-secret-jwt-key-change-in-production".to_string());

        let encoding_key = EncodingKey::from_secret(secret.as_ref());
        let decoding_key = DecodingKey::from_secret(secret.as_ref());

        Ok(Self {
            encoding_key,
            decoding_key,
            access_token_duration: Duration::hours(1),      // 1 hour
            refresh_token_duration: Duration::days(30),     // 30 days
        })
    }

    pub fn generate_token_pair(&self, user_id: Uuid, email: &str, role: &str) -> Result<TokenPair, Box<dyn std::error::Error>> {
        let now = Utc::now();
        let access_exp = now + self.access_token_duration;
        let refresh_exp = now + self.refresh_token_duration;

        // Access token claims
        let access_claims = Claims {
            sub: user_id.to_string(),
            email: email.to_string(),
            role: role.to_string(),
            exp: access_exp.timestamp(),
            iat: now.timestamp(),
            jti: Uuid::new_v4().to_string(),
        };

        // Refresh token claims (longer expiration)
        let refresh_claims = Claims {
            sub: user_id.to_string(),
            email: email.to_string(),
            role: role.to_string(),
            exp: refresh_exp.timestamp(),
            iat: now.timestamp(),
            jti: Uuid::new_v4().to_string(),
        };

        let access_token = encode(&Header::default(), &access_claims, &self.encoding_key)?;
        let refresh_token = encode(&Header::default(), &refresh_claims, &self.encoding_key)?;

        Ok(TokenPair {
            access_token,
            refresh_token,
            expires_in: self.access_token_duration.num_seconds(),
            token_type: "Bearer".to_string(),
        })
    }

    pub fn verify_token(&self, token: &str) -> Result<Claims, Box<dyn std::error::Error>> {
        let validation = Validation::new(Algorithm::HS256);
        let token_data = decode::<Claims>(token, &self.decoding_key, &validation)?;
        Ok(token_data.claims)
    }

    pub fn extract_token_from_header<'a>(&self, auth_header: &'a str) -> Option<&'a str> {
        if auth_header.starts_with("Bearer ") {
            Some(&auth_header[7..])
        } else {
            None
        }
    }
}

// Password utilities
pub fn hash_password(password: &str) -> Result<String, bcrypt::BcryptError> {
    hash(password, DEFAULT_COST)
}

pub fn verify_password(password: &str, hash: &str) -> Result<bool, bcrypt::BcryptError> {
    verify(password, hash)
}

// Auth errors
#[derive(Debug)]
pub enum AuthError {
    InvalidToken,
    ExpiredToken,
    MissingToken,
    InvalidCredentials,
    UserNotFound,
    EmailAlreadyExists,
    InsufficientPermissions,
}

impl std::fmt::Display for AuthError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AuthError::InvalidToken => write!(f, "Invalid token"),
            AuthError::ExpiredToken => write!(f, "Token has expired"),
            AuthError::MissingToken => write!(f, "Missing authentication token"),
            AuthError::InvalidCredentials => write!(f, "Invalid email or password"),
            AuthError::UserNotFound => write!(f, "User not found"),
            AuthError::EmailAlreadyExists => write!(f, "Email already exists"),
            AuthError::InsufficientPermissions => write!(f, "Insufficient permissions"),
        }
    }
}

impl std::error::Error for AuthError {}

// Global auth service instance
lazy_static::lazy_static! {
    pub static ref AUTH_SERVICE: AuthService = AuthService::new().expect("Failed to initialize auth service");
}
