use sqlx::{Pool, Postgres};
use std::time::Duration;
use tracing::info;

pub type DatabasePool = Pool<Postgres>;

pub async fn create_pool(database_url: &str) -> Result<DatabasePool, sqlx::Error> {
    info!("🗄️ Connecting to database...");

    let pool = sqlx::postgres::PgPoolOptions::new()
        // 🎯 Configurações otimizadas para Supabase Session Pooler
        .max_connections(5)                        // ⬇️ Menor para pooler
        .min_connections(1)                        // ⬇️ <PERSON>ín<PERSON> ainda menor
        .acquire_timeout(Duration::from_secs(5))   // ⬇️ Timeout mais agressivo
        .idle_timeout(Duration::from_secs(30))     // ⬇️ Idle mais curto
        .max_lifetime(Duration::from_secs(180))    // ⬇️ Vida mais curta (3 min)
        .test_before_acquire(true)                 // ✅ Testar conexões antes de usar
        .connect(database_url)
        .await?;

    info!("✅ Database connection established with optimized pool settings");
    Ok(pool)
}

// pub async fn run_migrations(pool: &DatabasePool) -> Result<(), sqlx::Error> {
//     info!("🔄 Running database migrations...");
//
//     sqlx::migrate!("./migrations")
//         .run(pool)
//         .await?;
//
//     info!("✅ Database migrations completed");
//     Ok(())
// }

pub async fn health_check(pool: &DatabasePool) -> Result<(), sqlx::Error> {
    sqlx::query("SELECT 1")
        .execute(pool)
        .await?;
    Ok(())
}
