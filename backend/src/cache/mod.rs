use redis::{Client, Connection, RedisResult};
use serde::{Deserialize, Serialize};
use serde_json;
use std::time::Duration;
use tracing::{info, warn, error};

#[derive(Clone)]
pub struct CacheService {
    client: Option<Client>,
    disabled: bool,
}

impl CacheService {
    pub fn new(redis_url: &str) -> Result<Self, redis::RedisError> {
        match Client::open(redis_url) {
            Ok(client) => {
                info!("🔄 Redis cache client created");
                Ok(Self {
                    client: Some(client),
                    disabled: false,
                })
            },
            Err(e) => {
                warn!("⚠️ Redis connection failed, creating disabled cache: {}", e);
                Ok(Self {
                    client: None,
                    disabled: true,
                })
            }
        }
    }

    pub fn new_disabled() -> Self {
        warn!("⚠️ Creating disabled cache service");
        Self {
            client: None,
            disabled: true,
        }
    }

    pub fn get_connection(&self) -> RedisResult<Connection> {
        match &self.client {
            Some(client) => client.get_connection(),
            None => Err(redis::RedisError::from((redis::ErrorKind::IoError, "Cache disabled"))),
        }
    }

    // Cache posts list
    pub async fn get_posts_cache(&self, cache_key: &str) -> Option<serde_json::Value> {
        if self.disabled {
            return None;
        }

        match self.get_connection() {
            Ok(mut conn) => {
                match redis::cmd("GET").arg(cache_key).query::<String>(&mut conn) {
                    Ok(cached_data) => {
                        match serde_json::from_str(&cached_data) {
                            Ok(posts) => {
                                info!("✅ Cache hit for posts: {}", cache_key);
                                Some(posts)
                            },
                            Err(e) => {
                                warn!("❌ Failed to deserialize cached posts: {}", e);
                                None
                            }
                        }
                    },
                    Err(_) => {
                        info!("❌ Cache miss for posts: {}", cache_key);
                        None
                    }
                }
            },
            Err(_) => {
                // Silently return None when cache is disabled
                None
            }
        }
    }

    pub async fn set_posts_cache(&self, cache_key: &str, posts: &serde_json::Value, ttl_seconds: u64) {
        if self.disabled {
            return;
        }

        match self.get_connection() {
            Ok(mut conn) => {
                match serde_json::to_string(posts) {
                    Ok(serialized) => {
                        let result: RedisResult<()> = redis::cmd("SETEX")
                            .arg(cache_key)
                            .arg(ttl_seconds)
                            .arg(serialized)
                            .query(&mut conn);

                        match result {
                            Ok(_) => info!("✅ Cached posts: {} (TTL: {}s)", cache_key, ttl_seconds),
                            Err(e) => error!("❌ Failed to cache posts: {}", e),
                        }
                    },
                    Err(e) => error!("❌ Failed to serialize posts for cache: {}", e),
                }
            },
            Err(e) => error!("❌ Failed to get Redis connection for caching: {}", e),
        }
    }

    // Cache single post
    pub async fn get_post_cache(&self, cache_key: &str) -> Option<serde_json::Value> {
        if self.disabled {
            return None;
        }

        match self.get_connection() {
            Ok(mut conn) => {
                match redis::cmd("GET").arg(cache_key).query::<String>(&mut conn) {
                    Ok(cached_data) => {
                        match serde_json::from_str(&cached_data) {
                            Ok(post) => {
                                info!("✅ Cache hit for post: {}", cache_key);
                                Some(post)
                            },
                            Err(e) => {
                                warn!("❌ Failed to deserialize cached post: {}", e);
                                None
                            }
                        }
                    },
                    Err(_) => {
                        info!("❌ Cache miss for post: {}", cache_key);
                        None
                    }
                }
            },
            Err(e) => {
                error!("❌ Failed to get Redis connection: {}", e);
                None
            }
        }
    }

    pub async fn set_post_cache(&self, cache_key: &str, post: &serde_json::Value, ttl_seconds: u64) {
        if self.disabled {
            return;
        }

        match self.get_connection() {
            Ok(mut conn) => {
                match serde_json::to_string(post) {
                    Ok(serialized) => {
                        let result: RedisResult<()> = redis::cmd("SETEX")
                            .arg(cache_key)
                            .arg(ttl_seconds)
                            .arg(serialized)
                            .query(&mut conn);

                        match result {
                            Ok(_) => info!("✅ Cached post: {} (TTL: {}s)", cache_key, ttl_seconds),
                            Err(e) => error!("❌ Failed to cache post: {}", e),
                        }
                    },
                    Err(e) => error!("❌ Failed to serialize post for cache: {}", e),
                }
            },
            Err(e) => error!("❌ Failed to get Redis connection for caching: {}", e),
        }
    }

    // Invalidate cache
    pub async fn invalidate_cache(&self, pattern: &str) {
        if self.disabled {
            return;
        }

        match self.get_connection() {
            Ok(mut conn) => {
                let result: RedisResult<()> = redis::cmd("DEL").arg(pattern).query(&mut conn);
                match result {
                    Ok(_) => info!("✅ Invalidated cache: {}", pattern),
                    Err(e) => error!("❌ Failed to invalidate cache: {}", e),
                }
            },
            Err(e) => error!("❌ Failed to get Redis connection for invalidation: {}", e),
        }
    }

    // Health check
    pub async fn health_check(&self) -> bool {
        if self.disabled {
            return true; // Cache disabled is considered "healthy"
        }

        match self.get_connection() {
            Ok(mut conn) => {
                match redis::cmd("PING").query::<String>(&mut conn) {
                    Ok(response) => {
                        if response == "PONG" {
                            info!("✅ Redis health check passed");
                            true
                        } else {
                            warn!("❌ Redis health check failed: unexpected response");
                            false
                        }
                    },
                    Err(e) => {
                        error!("❌ Redis health check failed: {}", e);
                        false
                    }
                }
            },
            Err(e) => {
                error!("❌ Failed to get Redis connection for health check: {}", e);
                false
            }
        }
    }
}

// Helper functions for cache keys
pub fn posts_cache_key(status: &Option<String>, author: &Option<String>, page: u32, limit: u32) -> String {
    format!(
        "posts:{}:{}:{}:{}",
        status.as_deref().unwrap_or("all"),
        author.as_deref().unwrap_or("all"),
        page,
        limit
    )
}

pub fn post_cache_key(id: &str) -> String {
    format!("post:id:{}", id)
}

pub fn post_slug_cache_key(slug: &str) -> String {
    format!("post:slug:{}", slug)
}
