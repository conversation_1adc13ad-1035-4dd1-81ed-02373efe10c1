use axum::{
    http::{HeaderMap, StatusCode, Request},
    middleware::Next,
    response::Response,
    body::Body,
};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::time::sleep;
use tracing::{info, warn};

// Rate limiter simples baseado em IP
#[derive(Clone)]
pub struct RateLimiter {
    requests: Arc<Mutex<HashMap<String, Vec<Instant>>>>,
    max_requests: usize,
    window: Duration,
}

impl RateLimiter {
    pub fn new(max_requests: usize, window: Duration) -> Self {
        Self {
            requests: Arc::new(Mutex::new(HashMap::new())),
            max_requests,
            window,
        }
    }

    pub fn check_rate_limit(&self, ip: &str) -> bool {
        let mut requests = self.requests.lock().unwrap();
        let now = Instant::now();
        
        // Limpar requests antigas
        let entry = requests.entry(ip.to_string()).or_insert_with(Vec::new);
        entry.retain(|&time| now.duration_since(time) < self.window);
        
        // Verificar se excedeu o limite
        if entry.len() >= self.max_requests {
            false
        } else {
            entry.push(now);
            true
        }
    }
}

// Middleware de rate limiting
pub async fn rate_limit_middleware(
    headers: HeaderMap,
    request: Request<Body>,
    next: Next<Body>,
) -> Result<Response, StatusCode> {
    // Extrair IP do cliente
    let client_ip = extract_client_ip(&headers);
    
    // Rate limiter: 100 requests por minuto por IP
    static RATE_LIMITER: std::sync::OnceLock<RateLimiter> = std::sync::OnceLock::new();
    let limiter = RATE_LIMITER.get_or_init(|| {
        RateLimiter::new(100, Duration::from_secs(60))
    });
    
    if !limiter.check_rate_limit(&client_ip) {
        warn!("🚫 Rate limit exceeded for IP: {}", client_ip);
        return Err(StatusCode::TOO_MANY_REQUESTS);
    }
    
    // Adicionar pequeno delay para reduzir carga de CPU
    sleep(Duration::from_millis(1)).await;
    
    Ok(next.run(request).await)
}

// Middleware de throttling (controle de CPU)
pub async fn cpu_throttle_middleware(
    request: Request<Body>,
    next: Next<Body>,
) -> Response {
    // Verificar carga do sistema (simulado)
    let cpu_usage = get_cpu_usage_estimate();
    
    if cpu_usage > 80.0 {
        warn!("🔥 High CPU usage detected: {:.1}%", cpu_usage);
        // Adicionar delay progressivo baseado na carga
        let delay_ms = ((cpu_usage - 80.0) * 10.0) as u64;
        sleep(Duration::from_millis(delay_ms.min(500))).await;
    }
    
    next.run(request).await
}

// Extrair IP do cliente
fn extract_client_ip(headers: &HeaderMap) -> String {
    // Tentar extrair IP de headers comuns
    if let Some(forwarded) = headers.get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded.to_str() {
            if let Some(ip) = forwarded_str.split(',').next() {
                return ip.trim().to_string();
            }
        }
    }
    
    if let Some(real_ip) = headers.get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            return ip_str.to_string();
        }
    }
    
    // Fallback para IP genérico
    "unknown".to_string()
}

// Estimativa simples de uso de CPU (placeholder)
fn get_cpu_usage_estimate() -> f64 {
    // Em produção, você usaria uma biblioteca como `sysinfo`
    // Por agora, retorna um valor baixo para desenvolvimento
    20.0
}

// Middleware de timeout para requests longos
pub async fn request_timeout_middleware(
    request: Request<Body>,
    next: Next<Body>,
) -> Result<Response, StatusCode> {
    // Timeout de 30 segundos para qualquer request
    match tokio::time::timeout(Duration::from_secs(30), next.run(request)).await {
        Ok(response) => Ok(response),
        Err(_) => {
            warn!("⏰ Request timeout after 30 seconds");
            Err(StatusCode::REQUEST_TIMEOUT)
        }
    }
}
