use axum::{
    routing::{get, post},
    Router, Json, Extension,
    http::StatusCode,
};
use serde::{Deserialize, Serialize};
use serde_json::json;
use tracing::{info, warn, error};
use validator::Validate;
use uuid::Uuid;

use crate::database::DatabasePool;
use crate::auth::{AuthService, AuthUser, hash_password, verify_password, AUTH_SERVICE};
use crate::models::user::{UserProfile, CreateUserProfileRequest};

#[derive(Debug, Deserialize, Validate)]
pub struct RegisterRequest {
    #[validate(email)]
    pub email: String,
    #[validate(length(min = 8, message = "Password must be at least 8 characters"))]
    pub password: String,
    #[validate(length(min = 2, max = 255))]
    pub full_name: Option<String>,
    pub bio: Option<String>,
    pub avatar_url: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(email)]
    pub email: String,
    #[validate(length(min = 1))]
    pub password: String,
}

#[derive(Debug, Deserialize)]
pub struct RefreshTokenRequest {
    pub refresh_token: String,
}

pub fn routes() -> Router {
    Router::new()
        // Public routes (no authentication required)
        .route("/register", post(register))
        .route("/login", post(login))
        .route("/refresh", post(refresh_token))
        // Protected routes (authentication required) - will be handled at app level
        .route("/me", get(get_current_user))
        .route("/logout", post(logout))
}

// Register new user
async fn register(
    Extension(pool): Extension<DatabasePool>,
    Json(payload): Json<RegisterRequest>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    info!("🔐 User registration attempt for: {}", payload.email);

    // 1. Validate input
    if let Err(errors) = payload.validate() {
        warn!("🔐 Registration validation failed: {:?}", errors);
        return Ok(Json(json!({
            "status": "error",
            "message": "Validation failed",
            "errors": errors
        })));
    }

    // 2. Check if user already exists
    let existing_user = sqlx::query("SELECT id FROM auth.users WHERE email = $1")
        .bind(&payload.email)
        .fetch_optional(&pool)
        .await
        .map_err(|e| {
            error!("🔐 Database error checking existing user: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    if existing_user.is_some() {
        warn!("🔐 Registration failed: email already exists: {}", payload.email);
        return Ok(Json(json!({
            "status": "error",
            "message": "Email already exists"
        })));
    }

    // 3. Hash password
    let password_hash = hash_password(&payload.password).map_err(|e| {
        error!("🔐 Password hashing failed: {}", e);
        StatusCode::INTERNAL_SERVER_ERROR
    })?;

    // 4. Create user in auth.users (for local development)
    let user_id = Uuid::new_v4();
    sqlx::query("INSERT INTO auth.users (id, email) VALUES ($1, $2)")
        .bind(user_id)
        .bind(&payload.email)
        .execute(&pool)
        .await
        .map_err(|e| {
            error!("🔐 Failed to create user: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    // 5. Create user profile
    sqlx::query("INSERT INTO user_profiles (id, full_name, bio, avatar_url, is_admin, is_active) VALUES ($1, $2, $3, $4, $5, $6)")
        .bind(user_id)
        .bind(&payload.full_name)
        .bind(&payload.bio)
        .bind(&payload.avatar_url)
        .bind(false) // New users are not admin by default
        .bind(true)  // New users are active by default
        .execute(&pool)
        .await
        .map_err(|e| {
            error!("🔐 Failed to create user profile: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    // 6. Generate JWT token
    let token_pair = AUTH_SERVICE.generate_token_pair(user_id, &payload.email, "user")
        .map_err(|e| {
            error!("🔐 Failed to generate tokens: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    info!("✅ User registered successfully: {}", payload.email);

    Ok(Json(json!({
        "status": "success",
        "message": "User registered successfully",
        "data": {
            "user": {
                "id": user_id,
                "email": payload.email,
                "full_name": payload.full_name
            },
            "tokens": token_pair
        }
    })))
}

// Login user (simplified for development)
async fn login(
    Extension(_pool): Extension<DatabasePool>,
    Json(payload): Json<LoginRequest>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    info!("🔐 User login attempt for: {}", payload.email);

    // 1. Validate input
    if let Err(errors) = payload.validate() {
        warn!("🔐 Login validation failed: {:?}", errors);
        return Ok(Json(json!({
            "status": "error",
            "message": "Validation failed",
            "errors": errors
        })));
    }

    // 2. Simplified login for development (hardcoded users)
    // TODO: Implement proper database queries after fixing SQLx issues

    let (user_id, is_admin, full_name) = match payload.email.as_str() {
        "<EMAIL>" => (
            "550e8400-e29b-41d4-a716-446655440000".parse().unwrap(),
            true,
            "Admin User"
        ),
        "<EMAIL>" => (
            "550e8400-e29b-41d4-a716-446655440001".parse().unwrap(),
            false,
            "Demo User"
        ),
        _ => {
            warn!("🔐 Login failed: user not found: {}", payload.email);
            return Ok(Json(json!({
                "status": "error",
                "message": "Invalid email or password"
            })));
        }
    };

    // 3. Generate JWT token
    let role = if is_admin { "admin" } else { "user" };
    let token_pair = AUTH_SERVICE.generate_token_pair(user_id, &payload.email, role)
        .map_err(|e| {
            error!("🔐 Failed to generate tokens: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    info!("✅ User logged in successfully: {}", payload.email);

    Ok(Json(json!({
        "status": "success",
        "message": "Login successful",
        "data": {
            "user": {
                "id": user_id,
                "email": payload.email,
                "full_name": full_name,
                "is_admin": is_admin
            },
            "tokens": token_pair
        }
    })))
}

// Refresh JWT token (simplified for development)
async fn refresh_token(
    Extension(_pool): Extension<DatabasePool>,
    Json(payload): Json<RefreshTokenRequest>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    info!("🔄 Token refresh attempt");

    // 1. Validate refresh token
    let claims = AUTH_SERVICE.verify_token(&payload.refresh_token)
        .map_err(|e| {
            warn!("🔄 Invalid refresh token: {}", e);
            StatusCode::UNAUTHORIZED
        })?;

    // 2. Generate new token pair (simplified - no database check for now)
    let new_token_pair = AUTH_SERVICE.generate_token_pair(
        claims.sub.parse().map_err(|_| StatusCode::UNAUTHORIZED)?,
        &claims.email,
        &claims.role
    ).map_err(|e| {
        error!("🔄 Failed to generate new tokens: {}", e);
        StatusCode::INTERNAL_SERVER_ERROR
    })?;

    info!("✅ Token refreshed successfully for user: {}", claims.email);

    Ok(Json(json!({
        "status": "success",
        "message": "Token refreshed successfully",
        "data": {
            "tokens": new_token_pair
        }
    })))
}

// Get current user info (temporary - no auth required for development)
async fn get_current_user() -> Result<Json<serde_json::Value>, StatusCode> {
    info!("👤 Get current user request (development mode)");

    // Return demo user data
    Ok(Json(json!({
        "status": "success",
        "data": {
            "user": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "email": "<EMAIL>",
                "full_name": "Admin User",
                "bio": null,
                "avatar_url": null,
                "is_admin": true,
                "is_active": true,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        }
    })))
}

// Logout user (temporary - no auth required for development)
async fn logout() -> Result<Json<serde_json::Value>, StatusCode> {
    info!("🚪 User logout (development mode)");

    // For JWT-based auth, logout is typically handled client-side
    // by removing the token from storage.

    info!("✅ User logged out successfully");

    Ok(Json(json!({
        "status": "success",
        "message": "Logout successful"
    })))
}
