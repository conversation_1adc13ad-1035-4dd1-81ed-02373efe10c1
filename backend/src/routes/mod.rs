use axum::{
    routing::{get, post, put, delete},
    Router,
};

pub mod auth;
pub mod posts;
pub mod users;
pub mod categories;
pub mod search;

pub fn create_routes() -> Router {
    Router::new()
        // Authentication routes
        .nest("/api/auth", auth::routes())

        // Posts routes
        .nest("/api/posts", posts::routes())

        // Users routes
        .nest("/api/users", users::routes())

        // Categories routes
        .nest("/api/categories", categories::routes())

        // Search routes
        .nest("/api/search", search::routes())
}
