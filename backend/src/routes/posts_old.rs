use axum::{
    routing::{get, post, put, delete},
    Router, Json, Extension,
    http::StatusCode,
    extract::{Path, Query},
};
use serde_json::{json, Value};
use serde::Deserialize;
use tracing::{info, warn, error};

use crate::database::DatabasePool;

#[derive(Deserialize)]
struct PostsQuery {
    page: Option<u32>,
    limit: Option<u32>,
    status: Option<String>,
    author: Option<String>,
    category: Option<String>,
}

pub fn routes() -> Router {
    Router::new()
        .route("/", get(list_posts).post(create_post))
        .route("/:id", get(get_post).put(update_post).delete(delete_post))
        .route("/slug/:slug", get(get_post_by_slug))
        .route("/:id/publish", post(publish_post))
        .route("/:id/unpublish", post(unpublish_post))
        .route("/:id/like", post(like_post))
        .route("/:id/view", post(increment_view))
}

// List posts with pagination and filters
async fn list_posts(
    Extension(pool): Extension<DatabasePool>,
    Query(params): Query<PostsQuery>,
) -> Result<Json<Value>, StatusCode> {
    info!("📝 List posts request - connecting to Supabase");

    let page = params.page.unwrap_or(1);
    let limit = params.limit.unwrap_or(10);
    let offset = (page - 1) * limit;

    // 🎯 IMPLEMENTAÇÃO REAL: Buscar posts do Supabase
    let mut query = "SELECT * FROM posts WHERE 1=1".to_string();

    // Aplicar filtros
    if let Some(status) = &params.status {
        query.push_str(&format!(" AND status = '{}'", status));
    }

    if let Some(author) = &params.author {
        query.push_str(&format!(" AND author_id = '{}'", author));
    }

    // Ordenação e paginação
    query.push_str(" ORDER BY published_at DESC");
    query.push_str(&format!(" LIMIT {} OFFSET {}", limit, offset));

    match sqlx::query(&query)
        .fetch_all(&pool)
        .await
    {
        Ok(rows) => {
            let posts: Vec<serde_json::Value> = rows.iter().map(|row| {
                json!({
                    "id": row.get::<uuid::Uuid, _>("id"),
                    "title": row.get::<String, _>("title"),
                    "content": row.get::<String, _>("content"),
                    "slug": row.get::<String, _>("slug"),
                    "status": row.get::<String, _>("status"),
                    "published_at": row.get::<Option<chrono::DateTime<chrono::Utc>>, _>("published_at"),
                    "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
                    "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at"),
                    "author_id": row.get::<uuid::Uuid, _>("author_id"),
                    "language": row.get::<String, _>("language"),
                    "views": row.get::<i32, _>("views")
                })
            }).collect();

            info!("✅ Found {} posts", posts.len());
            Ok(Json(json!({
                "status": "success",
                "data": posts,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": posts.len()
                }
            })))
        },
        Err(e) => {
            error!("❌ Failed to fetch posts: {}", e);
            Ok(Json(json!({
                "status": "error",
                "message": "Failed to fetch posts",
                "error": e.to_string()
            })))
        }
    }
        "data": {
            "posts": [
                {
                    "id": "750e8400-e29b-41d4-a716-************",
                    "title": "Welcome to Blueprint Blog",
                    "slug": "welcome-to-blueprint-blog",
                    "excerpt": "This is the first post on our new blog platform.",
                    "author": {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "name": "Admin User"
                    },
                    "status": "published",
                    "published_at": "2024-01-01T00:00:00Z",
                    "reading_time": 3,
                    "views": 0,
                    "likes": 0
                }
            ],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": 1,
                "total_pages": 1
            }
        }
    })))
}

// Get single post by ID
async fn get_post(
    Extension(pool): Extension<DatabasePool>,
    Path(id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("📖 Get post request: {}", id);

    // TODO: Implement get single post
    // 1. Query post by ID
    // 2. Join with author info
    // 3. Join with categories
    // 4. Return full post data

    Ok(Json(json!({
        "status": "success",
        "data": {
            "post": {
                "id": id,
                "title": "Sample Post",
                "slug": "sample-post",
                "content": "# Sample Post\n\nThis is a sample post content.",
                "author": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "name": "Admin User"
                },
                "status": "published",
                "published_at": "2024-01-01T00:00:00Z"
            }
        }
    })))
}

// Create new post
async fn create_post(
    Extension(pool): Extension<DatabasePool>,
    Json(payload): Json<Value>,
) -> Result<Json<Value>, StatusCode> {
    info!("✍️ Create post request");

    // TODO: Implement post creation
    // 1. Validate input
    // 2. Extract author from JWT
    // 3. Generate slug from title
    // 4. Insert post into database
    // 5. Return created post

    Ok(Json(json!({
        "status": "success",
        "message": "Post created successfully",
        "data": {
            "post": {
                "id": "new-post-id",
                "title": "New Post",
                "slug": "new-post",
                "status": "draft"
            }
        }
    })))
}

// Update existing post
async fn update_post(
    Extension(pool): Extension<DatabasePool>,
    Path(id): Path<String>,
    Json(payload): Json<Value>,
) -> Result<Json<Value>, StatusCode> {
    info!("✏️ Update post request: {}", id);

    // TODO: Implement post update
    // 1. Validate input
    // 2. Check if post exists
    // 3. Check if user owns post or is admin
    // 4. Update post in database
    // 5. Return updated post

    Ok(Json(json!({
        "status": "success",
        "message": "Post updated successfully",
        "data": {
            "post": {
                "id": id,
                "title": "Updated Post",
                "status": "draft"
            }
        }
    })))
}

// Delete post
async fn delete_post(
    Extension(pool): Extension<DatabasePool>,
    Path(id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("🗑️ Delete post request: {}", id);

    // TODO: Implement post deletion
    // 1. Check if post exists
    // 2. Check if user owns post or is admin
    // 3. Delete post from database
    // 4. Return success message

    Ok(Json(json!({
        "status": "success",
        "message": "Post deleted successfully"
    })))
}

// Publish post
async fn publish_post(
    Extension(pool): Extension<DatabasePool>,
    Path(id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("📢 Publish post request: {}", id);

    // TODO: Implement post publishing
    // 1. Check if post exists
    // 2. Check if user owns post or is admin
    // 3. Update status to 'published'
    // 4. Set published_at timestamp

    Ok(Json(json!({
        "status": "success",
        "message": "Post published successfully"
    })))
}

// Unpublish post
async fn unpublish_post(
    Extension(pool): Extension<DatabasePool>,
    Path(id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("📝 Unpublish post request: {}", id);

    // TODO: Implement post unpublishing
    // 1. Check if post exists
    // 2. Check if user owns post or is admin
    // 3. Update status to 'draft'
    // 4. Clear published_at timestamp

    Ok(Json(json!({
        "status": "success",
        "message": "Post unpublished successfully"
    })))
}

// Like post
async fn like_post(
    Extension(pool): Extension<DatabasePool>,
    Path(id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("❤️ Like post request: {}", id);

    // TODO: Implement post liking
    // 1. Check if post exists
    // 2. Increment likes counter
    // 3. Track user likes (prevent duplicates)

    Ok(Json(json!({
        "status": "success",
        "message": "Post liked successfully",
        "data": {
            "likes": 1
        }
    })))
}

// Increment view count
async fn increment_view(
    Extension(pool): Extension<DatabasePool>,
    Path(id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("👁️ Increment view request: {}", id);

    // TODO: Implement view counting
    // 1. Check if post exists
    // 2. Increment views counter
    // 3. Track unique views (optional)

    Ok(Json(json!({
        "status": "success",
        "message": "View counted successfully",
        "data": {
            "views": 1
        }
    })))
}
