use axum::{
    routing::{get, post, put, delete},
    Router, Json, Extension,
    http::StatusCode,
    extract::{Path, Query},
};
use serde_json::{json, Value};
use serde::Deserialize;
use tracing::{info, warn, error};
use sqlx::Row;

use crate::database::DatabasePool;
use crate::cache::{CacheService, posts_cache_key, post_cache_key, post_slug_cache_key};

#[derive(Deserialize)]
struct PostsQuery {
    page: Option<u32>,
    limit: Option<u32>,
    status: Option<String>,
    author: Option<String>,
    category: Option<String>,
    language: Option<String>,
}

pub fn routes() -> Router {
    Router::new()
        .route("/", get(list_posts).post(create_post))
        .route("/:id", get(get_post).put(update_post).delete(delete_post))
        .route("/slug/:slug", get(get_post_by_slug))
        .route("/:id/publish", post(publish_post))
        .route("/:id/unpublish", post(unpublish_post))
        .route("/:id/like", post(like_post))
        .route("/:id/view", post(increment_view))
}

// List posts with pagination and filters
async fn list_posts(
    Extension(pool): Extension<DatabasePool>,
    Extension(cache): Extension<CacheService>,
    Query(params): Query<PostsQuery>,
) -> Result<Json<Value>, StatusCode> {
    info!("📝 List posts request - connecting to Supabase");

    let page = params.page.unwrap_or(1);
    let limit = params.limit.unwrap_or(10);
    let offset = (page - 1) * limit;

    // 🔄 Verificar cache primeiro
    let cache_key = posts_cache_key(&params.status, &params.author, page, limit);
    if let Some(cached_posts) = cache.get_posts_cache(&cache_key).await {
        info!("✅ Returning cached posts");
        return Ok(Json(cached_posts));
    }

    // 🎯 IMPLEMENTAÇÃO OTIMIZADA: Prepared statements com parâmetros
    info!("🔍 Fetching posts with filters - status: {:?}, author: {:?}", params.status, params.author);

    // Converter author string para UUID se fornecido
    let author_uuid = if let Some(author_str) = &params.author {
        match uuid::Uuid::parse_str(author_str) {
            Ok(uuid) => Some(uuid),
            Err(_) => {
                info!("❌ Invalid author UUID format: {}", author_str);
                return Ok(Json(json!({
                    "status": "error",
                    "message": "Invalid author UUID format"
                })));
            }
        }
    } else {
        None
    };

    // ✅ Query dinâmica para evitar conflitos de prepared statements no Supabase Session Pooler
    // 🎯 Usando os nomes corretos das colunas do Supabase
    let mut query = "SELECT id, title, content, slug, excerpt, featured_image, status, published_at, created_at, updated_at,
                            author_id, language, tags, reading_time, views, likes
                     FROM posts WHERE 1=1".to_string();

    let mut query_params: Vec<String> = Vec::new();

    if let Some(status) = &params.status {
        query.push_str(&format!(" AND status = '{}'", status));
    }

    if let Some(author_uuid) = &author_uuid {
        query.push_str(&format!(" AND author_id = '{}'", author_uuid));
    }

    if let Some(language) = &params.language {
        query.push_str(&format!(" AND language = '{}'", language));
    }

    query.push_str(" ORDER BY published_at DESC NULLS LAST, created_at DESC");
    query.push_str(&format!(" LIMIT {} OFFSET {}", limit, offset));

    match sqlx::query(&query)
        .persistent(false)  // 🎯 Não usar prepared statements para evitar conflitos no Supabase Session Pooler
        .fetch_all(&pool)
        .await
    {
        Ok(rows) => {
            let posts: Vec<serde_json::Value> = rows.iter().map(|row| {
                json!({
                    "id": row.get::<uuid::Uuid, _>("id"),
                    "title": row.get::<String, _>("title"),
                    "content": row.get::<String, _>("content"),
                    "slug": row.get::<String, _>("slug"),
                    "excerpt": row.get::<Option<String>, _>("excerpt"),
                    "featured_image": row.get::<Option<String>, _>("featured_image"),
                    "status": row.get::<String, _>("status"),
                    "published_at": row.get::<Option<chrono::DateTime<chrono::Utc>>, _>("published_at"),
                    "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
                    "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at"),
                    "author_id": row.get::<uuid::Uuid, _>("author_id"),
                    "language": row.get::<Option<String>, _>("language").unwrap_or_else(|| "pt".to_string()),
                    "tags": row.get::<Option<Vec<String>>, _>("tags"), // tags como array no Supabase
                    "reading_time": row.get::<Option<i32>, _>("reading_time"),
                    "views": row.get::<Option<i32>, _>("views").unwrap_or(0),
                    "likes": row.get::<Option<i32>, _>("likes").unwrap_or(0),
                    "categories": null, // TODO: Implementar join com tabela categories
                    "author_name": null // TODO: Implementar join com tabela users
                })
            }).collect();

            info!("✅ Found {} posts", posts.len());

            let response = json!({
                "status": "success",
                "data": posts,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": posts.len()
                }
            });

            // 🔄 Cachear resultado por 5 minutos
            cache.set_posts_cache(&cache_key, &response, 300).await;

            Ok(Json(response))
        },
        Err(e) => {
            error!("❌ Failed to fetch posts: {}", e);
            Ok(Json(json!({
                "status": "error",
                "message": "Failed to fetch posts",
                "error": e.to_string()
            })))
        }
    }
}

// Get single post by ID
async fn get_post(
    Extension(pool): Extension<DatabasePool>,
    Extension(cache): Extension<CacheService>,
    Path(id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("📖 Get post request: {}", id);

    // 🔄 Verificar cache primeiro
    let cache_key = post_cache_key(&id);
    if let Some(cached_post) = cache.get_post_cache(&cache_key).await {
        info!("✅ Returning cached post");
        return Ok(Json(cached_post));
    }

    // Validar UUID
    let post_id = match uuid::Uuid::parse_str(&id) {
        Ok(uuid) => uuid,
        Err(_) => {
            info!("❌ Invalid UUID format: {}", id);
            return Ok(Json(json!({
                "status": "error",
                "message": "Invalid post ID format"
            })));
        }
    };

    // ✅ Query otimizada - usando apenas colunas que existem no Supabase
    let query = "SELECT id, title, content, slug, status, published_at, created_at, updated_at, author_id, views
                 FROM posts WHERE id = $1";

    match sqlx::query(query)
        .bind(post_id)
        .persistent(false)  // 🎯 Não usar prepared statements
        .fetch_optional(&pool)
        .await
    {
        Ok(Some(row)) => {
            let post = json!({
                "id": row.get::<uuid::Uuid, _>("id"),
                "title": row.get::<String, _>("title"),
                "content": row.get::<String, _>("content"),
                "slug": row.get::<String, _>("slug"),
                "status": row.get::<String, _>("status"),
                "published_at": row.get::<Option<chrono::DateTime<chrono::Utc>>, _>("published_at"),
                "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
                "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at"),
                "author_id": row.get::<uuid::Uuid, _>("author_id"),
                "views": row.get::<Option<i32>, _>("views").unwrap_or(0),
                // 🎯 Campos padrão para compatibilidade
                "language": "pt",
                "tags": null,
                "categories": null,
                "author_name": null
            });

            info!("✅ Found post: {}", row.get::<String, _>("title"));

            let response = json!({
                "status": "success",
                "data": post
            });

            // 🔄 Cachear post por 10 minutos
            cache.set_post_cache(&cache_key, &response, 600).await;

            Ok(Json(response))
        },
        Ok(None) => {
            info!("❌ Post not found: {}", id);
            Ok(Json(json!({
                "status": "error",
                "message": "Post not found"
            })))
        },
        Err(e) => {
            error!("❌ Failed to fetch post: {}", e);
            Ok(Json(json!({
                "status": "error",
                "message": "Failed to fetch post",
                "error": e.to_string()
            })))
        }
    }
}

// Get single post by slug
async fn get_post_by_slug(
    Extension(pool): Extension<DatabasePool>,
    Extension(cache): Extension<CacheService>,
    Path(slug): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("📖 Get post by slug request: {}", slug);

    // 🔄 Verificar cache primeiro
    let cache_key = post_slug_cache_key(&slug);
    if let Some(cached_post) = cache.get_post_cache(&cache_key).await {
        info!("✅ Returning cached post by slug");
        return Ok(Json(cached_post));
    }

    // ✅ Query otimizada - usando apenas colunas que existem no Supabase
    let query = "SELECT id, title, content, slug, status, published_at, created_at, updated_at, author_id, views
                 FROM posts WHERE slug = $1 AND status = 'published'";

    match sqlx::query(query)
        .bind(&slug)
        .persistent(false)  // 🎯 Não usar prepared statements
        .fetch_optional(&pool)
        .await
    {
        Ok(Some(row)) => {
            let post = json!({
                "id": row.get::<uuid::Uuid, _>("id"),
                "title": row.get::<String, _>("title"),
                "content": row.get::<String, _>("content"),
                "slug": row.get::<String, _>("slug"),
                "status": row.get::<String, _>("status"),
                "published_at": row.get::<Option<chrono::DateTime<chrono::Utc>>, _>("published_at"),
                "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
                "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at"),
                "author_id": row.get::<uuid::Uuid, _>("author_id"),
                "views": row.get::<Option<i32>, _>("views").unwrap_or(0),
                // 🎯 Campos padrão para compatibilidade
                "language": "pt",
                "tags": null,
                "categories": null,
                "author_name": null
            });

            info!("✅ Found post by slug: {}", row.get::<String, _>("title"));

            let response = json!({
                "status": "success",
                "data": post
            });

            // 🔄 Cachear post por slug por 10 minutos
            cache.set_post_cache(&cache_key, &response, 600).await;

            Ok(Json(response))
        },
        Ok(None) => {
            info!("❌ Post not found by slug: {}", slug);
            Ok(Json(json!({
                "status": "error",
                "message": "Post not found"
            })))
        },
        Err(e) => {
            error!("❌ Failed to fetch post by slug: {}", e);
            Ok(Json(json!({
                "status": "error",
                "message": "Failed to fetch post",
                "error": e.to_string()
            })))
        }
    }
}

// Create new post
async fn create_post(
    Extension(_pool): Extension<DatabasePool>,
    Json(_payload): Json<Value>,
) -> Result<Json<Value>, StatusCode> {
    info!("📝 Create post request");

    // TODO: Implement post creation
    Ok(Json(json!({
        "status": "success",
        "message": "Post created successfully"
    })))
}

// Update existing post
async fn update_post(
    Extension(_pool): Extension<DatabasePool>,
    Path(_id): Path<String>,
    Json(_payload): Json<Value>,
) -> Result<Json<Value>, StatusCode> {
    info!("✏️ Update post request");

    // TODO: Implement post update
    Ok(Json(json!({
        "status": "success",
        "message": "Post updated successfully"
    })))
}

// Delete post
async fn delete_post(
    Extension(_pool): Extension<DatabasePool>,
    Path(_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("🗑️ Delete post request");

    // TODO: Implement post deletion
    Ok(Json(json!({
        "status": "success",
        "message": "Post deleted successfully"
    })))
}

// Publish post
async fn publish_post(
    Extension(_pool): Extension<DatabasePool>,
    Path(_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("📢 Publish post request");

    // TODO: Implement post publishing
    Ok(Json(json!({
        "status": "success",
        "message": "Post published successfully"
    })))
}

// Unpublish post
async fn unpublish_post(
    Extension(_pool): Extension<DatabasePool>,
    Path(_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("📝 Unpublish post request");

    // TODO: Implement post unpublishing
    Ok(Json(json!({
        "status": "success",
        "message": "Post unpublished successfully"
    })))
}

// Like post
async fn like_post(
    Extension(_pool): Extension<DatabasePool>,
    Path(_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("❤️ Like post request");

    // TODO: Implement post liking
    Ok(Json(json!({
        "status": "success",
        "message": "Post liked successfully"
    })))
}

// Increment view count
async fn increment_view(
    Extension(_pool): Extension<DatabasePool>,
    Path(_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    info!("👁️ Increment view request");

    // TODO: Implement view counting with bot detection
    Ok(Json(json!({
        "status": "success",
        "message": "View counted successfully"
    })))
}
