use std::fs;
use tracing::info;
use tracing_subscriber::{
    fmt,
    EnvFilter,
};

pub struct LogConfig {
    pub level: String,
    pub console_enabled: bool,
    pub file_enabled: bool,
    pub json_enabled: bool,
    pub log_dir: String,
    pub max_files: usize,
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            console_enabled: true,
            file_enabled: true,
            json_enabled: true,
            log_dir: "./logs".to_string(),
            max_files: 30,
        }
    }
}

pub fn init_logging(config: LogConfig) -> Result<(), Box<dyn std::error::Error>> {
    // Create logs directory if it doesn't exist
    if config.file_enabled {
        fs::create_dir_all(&config.log_dir)?;
    }

    // Create env filter
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(&config.level));

    // Simple console-only approach for now
    tracing_subscriber::fmt()
        .with_env_filter(env_filter)
        .with_target(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_ansi(config.console_enabled)
        .init();

    info!("🚀 Logging system initialized");
    info!("📊 Log level: {}", config.level);
    info!("🖥️ Console logging: {}", config.console_enabled);
    info!("📁 File logging: {} (will be implemented later)", config.file_enabled);

    if config.file_enabled {
        info!("📂 Log directory: {}", config.log_dir);
    }

    Ok(())
}

// Middleware for request logging
pub mod middleware {
    use axum::{
        http::Request,
        middleware::Next,
        response::Response,
        body::Body,
    };
    use std::time::Instant;
    use tracing::{info, warn, error};
    use uuid::Uuid;

    pub async fn request_logging(
        request: Request<Body>,
        next: Next<Body>,
    ) -> Response {
        let start = Instant::now();
        let request_id = Uuid::new_v4();
        let method = request.method().clone();
        let uri = request.uri().clone();
        let version = request.version();

        // Get user agent and IP if available
        let user_agent = request
            .headers()
            .get("user-agent")
            .and_then(|h| h.to_str().ok())
            .unwrap_or("unknown");

        let forwarded_for = request
            .headers()
            .get("x-forwarded-for")
            .and_then(|h| h.to_str().ok())
            .unwrap_or("unknown");

        // Create span for this request
        let span = tracing::info_span!(
            "http_request",
            request_id = %request_id,
            method = %method,
            uri = %uri,
            version = ?version,
            user_agent = user_agent,
            forwarded_for = forwarded_for,
        );

        let _enter = span.enter();

        info!("🌐 Request started");

        // Process request
        let response = next.run(request).await;

        let duration = start.elapsed();
        let status = response.status();

        // Log based on status code
        match status.as_u16() {
            200..=299 => {
                info!(
                    status = %status,
                    duration_ms = duration.as_millis(),
                    "✅ Request completed successfully"
                );
            }
            300..=399 => {
                info!(
                    status = %status,
                    duration_ms = duration.as_millis(),
                    "🔄 Request redirected"
                );
            }
            400..=499 => {
                warn!(
                    status = %status,
                    duration_ms = duration.as_millis(),
                    "⚠️ Client error"
                );
            }
            500..=599 => {
                error!(
                    status = %status,
                    duration_ms = duration.as_millis(),
                    "❌ Server error"
                );
            }
            _ => {
                info!(
                    status = %status,
                    duration_ms = duration.as_millis(),
                    "📊 Request completed"
                );
            }
        }

        response
    }
}

// Log rotation and cleanup utilities
pub mod rotation {
    use std::fs;
    use std::path::Path;
    use std::time::{SystemTime, UNIX_EPOCH};
    use tracing::{info, warn};

    pub fn cleanup_old_logs(log_dir: &str, max_age_days: u64) -> Result<(), Box<dyn std::error::Error>> {
        let log_path = Path::new(log_dir);
        if !log_path.exists() {
            return Ok(());
        }

        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
        let max_age_seconds = max_age_days * 24 * 60 * 60;
        let mut removed_count = 0;

        for entry in fs::read_dir(log_path)? {
            let entry = entry?;
            let metadata = entry.metadata()?;

            if metadata.is_file() {
                if let Ok(modified) = metadata.modified() {
                    if let Ok(modified_duration) = modified.duration_since(UNIX_EPOCH) {
                        let file_age = now - modified_duration.as_secs();

                        if file_age > max_age_seconds {
                            match fs::remove_file(entry.path()) {
                                Ok(_) => {
                                    info!("🗑️ Removed old log file: {:?}", entry.file_name());
                                    removed_count += 1;
                                }
                                Err(e) => {
                                    warn!("⚠️ Failed to remove log file {:?}: {}", entry.file_name(), e);
                                }
                            }
                        }
                    }
                }
            }
        }

        if removed_count > 0 {
            info!("🧹 Cleaned up {} old log files", removed_count);
        }

        Ok(())
    }

    pub fn get_log_stats(log_dir: &str) -> Result<LogStats, Box<dyn std::error::Error>> {
        let log_path = Path::new(log_dir);
        if !log_path.exists() {
            return Ok(LogStats::default());
        }

        let mut stats = LogStats::default();

        for entry in fs::read_dir(log_path)? {
            let entry = entry?;
            let metadata = entry.metadata()?;

            if metadata.is_file() {
                stats.file_count += 1;
                stats.total_size += metadata.len();

                if let Some(name) = entry.file_name().to_str() {
                    if name.ends_with(".log") {
                        stats.text_files += 1;
                    } else if name.ends_with(".json") {
                        stats.json_files += 1;
                    }
                }
            }
        }

        Ok(stats)
    }

    #[derive(Debug, Default)]
    pub struct LogStats {
        pub file_count: usize,
        pub text_files: usize,
        pub json_files: usize,
        pub total_size: u64,
    }

    impl LogStats {
        pub fn total_size_mb(&self) -> f64 {
            self.total_size as f64 / 1024.0 / 1024.0
        }
    }
}
