# Nginx Único - Proxy para Frontend + Backend
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    client_max_body_size 16M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        application/javascript
        application/json
        text/css
        text/javascript
        text/plain
        text/xml;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;

    # Upstream para backend
    upstream backend {
        server backend:3001;
        keepalive 32;
    }

    # Upstream removido - frontend agora é servido como arquivos estáticos

    server {
        listen 80 default_server;
        server_name _;

        # Security
        server_tokens off;

        # Rate limiting geral
        limit_req zone=general burst=50 nodelay;

        # Health check endpoint (direto do nginx)
        location /health {
            access_log off;
            return 200 "OK\n";
            add_header Content-Type text/plain;
        }

        # API Backend - Proxy reverso
        location /api/ {
            # Rate limiting específico para API
            limit_req zone=api burst=20 nodelay;

            # Proxy para backend Rust
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Rota removida - Next.js não é mais usado

        # Frontend - Arquivos estáticos
        location / {
            root /usr/share/nginx/html;
            index index.html;

            # Handle client-side routing (SPA)
            try_files $uri $uri/ /index.html;

            # Cache HTML files for short time
            location ~* \.html$ {
                expires 5m;
                add_header Cache-Control "public";
            }
        }

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            root /usr/share/nginx/html;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        # Block access to hidden files
        location ~ /\. {
            deny all;
            return 404;
        }
    }
}
