- Fluxo de conexão:

+-------------------+ +---------------------+ +-------------------+
| Frontend (Vite)| <---> | NGINX (proxy) | <---> | Backend (Rust) |
+-------------------+ +---------------------+ +-------------------+
|
v
+-------------------+
| Supabase (DB) |
+-------------------+

- Esquema de diretorios:
  /blueprint-blog
  ├── /frontend
  │ ├── Dockerfile
  │ ├── nginx.conf (frontend serve only in prod, or via NGINX upstream)
  │ └── .env.production
  │
  ├── /backend
  │ ├── Dockerfile
  │ ├── .env.production
  │
  ├── /nginx
  │ └── nginx.conf <-- Este é o que gerencia TUDO (Front + Backend)
  │
  ├── docker-compose.yml
  ├── .env (ou .env.build central opcional)
  └── Makefile

- Fluxo de comunição local:
  Frontend → http://localhost:5173 (Vite dev server)
  Backend → http://localhost:3001 (Rust)

- Fluxo em produção:
  Frontend → NGINX → serve o frontend static /usr/share/nginx/html
  Backend → NGINX → proxy_pass /api → http://backend:3001/api

- Exemplo em produção:
  https://blueprintblog.tech/ → Frontend (React static)
  https://blueprintblog.tech/api/posts → Backend Rust API

---

# 🚀 FLUXOS COMPLETOS DE DESENVOLVIMENTO E PRODUÇÃO

## 🔧 AMBIENTE DE DESENVOLVIMENTO

### 📋 Pré-requisitos

```bash
# Instalar dependências básicas
sudo apt update && sudo apt install -y curl wget git build-essential

# Docker e Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Verificar instalações
docker --version && node --version && cargo --version
```

### 🏗️ Setup do Projeto (Desenvolvimento)

```bash
# 1. Clonar repositório
git clone https://github.com/user/blueprint-blog.git
cd blueprint-blog

# 2. Configurar variáveis de ambiente
cp .env.example .env
# Editar .env com configurações de desenvolvimento

# 3. Instalar dependências do frontend
cd frontend
npm install
cd ..

# 4. Build do backend (verificar se compila)
cd backend
cargo check
cargo build
cd ..

# 5. Subir ambiente de desenvolvimento
docker-compose -f docker-compose-dev.yml up -d

# 6. Verificar se está funcionando
curl http://localhost/health
curl http://localhost:3001/api/health
```

### 🔄 Fluxo de Desenvolvimento Diário

```bash
# Iniciar ambiente
docker-compose -f docker-compose-dev.yml up -d

# Verificar logs
docker-compose -f docker-compose-dev.yml logs -f

# Desenvolvimento frontend (hot reload)
cd frontend
npm run dev
# Acesso: http://localhost:5173

# Desenvolvimento backend (com logs)
cd backend
RUST_LOG=debug cargo run
# Acesso: http://localhost:3001

# Testes
cd frontend && npm test
cd backend && cargo test

# Parar ambiente
docker-compose -f docker-compose-dev.yml down
```

### 🌐 Arquitetura de Desenvolvimento

```
┌─────────────────────────────────────────────────────────────┐
│                    💻 Ambiente Local                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  🌐 Frontend    │    │  🦀 Backend     │                │
│  │  Vite Dev       │────│  Cargo Run      │                │
│  │  Port 5173      │    │  Port 3001      │                │
│  │  Hot Reload     │    │  Debug Logs     │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                         │
│           │                       │                         │
│           ▼                       ▼                         │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  🔀 Nginx       │    │  🗄️ Redis       │                │
│  │  Port 80        │    │  Port 6379      │                │
│  │  Proxy (teste)  │    │  Cache (dev)    │                │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────┐
│                ☁️ Supabase PostgreSQL                       │
│                Session Pooler                              │
└─────────────────────────────────────────────────────────────┘

Fluxo de Dados (Desenvolvimento):
1. Frontend (5173) → Backend (3001) → Supabase
2. NGINX (80) → Frontend/Backend (para testes)
3. Redis (6379) → Cache local
```

### 🔧 Comandos Úteis (Desenvolvimento)

```bash
# Logs específicos
docker-compose -f docker-compose-dev.yml logs backend
docker-compose -f docker-compose-dev.yml logs frontend
docker-compose -f docker-compose-dev.yml logs nginx

# Executar comandos nos containers
docker-compose -f docker-compose-dev.yml exec backend cargo check
docker-compose -f docker-compose-dev.yml exec frontend npm run build

# Rebuild específico
docker-compose -f docker-compose-dev.yml build backend
docker-compose -f docker-compose-dev.yml up -d backend

# Debug de rede
docker-compose -f docker-compose-dev.yml exec frontend ping backend
docker-compose -f docker-compose-dev.yml exec backend curl http://frontend:3000

# Limpar ambiente
docker-compose -f docker-compose-dev.yml down -v
docker system prune -f
```

---

## 🚀 AMBIENTE DE PRODUÇÃO

### 📋 Pré-requisitos do Servidor

```bash
# Servidor Ubuntu 22.04 LTS (recomendado)
# Mínimo: 2 CPU, 4GB RAM, 20GB SSD

# Atualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar dependências essenciais
sudo apt install -y curl wget git ufw fail2ban

# Docker e Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Nginx (para SSL e proxy)
sudo apt install nginx certbot python3-certbot-nginx

# Configurar firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 🏗️ Setup do Projeto (Produção)

```bash
# 1. Preparar diretório
sudo mkdir -p /opt/blueprint-blog
sudo chown $USER:$USER /opt/blueprint-blog
cd /opt/blueprint-blog

# 2. Clonar repositório
git clone https://github.com/user/blueprint-blog.git .

# 3. Configurar variáveis de ambiente
cp .env.example .env
nano .env
# Configurar:
# - DATABASE_URL (Supabase produção)
# - JWT_SECRET (seguro)
# - RUST_ENV=production
# - NODE_ENV=production

# 4. Build e deploy
docker-compose -f docker-compose-prod.yml build
docker-compose -f docker-compose-prod.yml up -d

# 5. Configurar SSL
sudo certbot --nginx -d blueprintblog.tech -d www.blueprintblog.tech

# 6. Verificar funcionamento
curl -I https://blueprintblog.tech/health
curl -I https://blueprintblog.tech/api/health
```

### 🌐 Arquitetura de Produção

```
                    🌍 Internet
                         │
                         ▼
┌─────────────────────────────────────────────────────────────┐
│                 🔒 Cloudflare/DNS                           │
│                 SSL Termination                            │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                🖥️ Servidor VPS                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────────┤
│  │                🔀 Nginx (Host)                          │
│  │                Port 80/443                             │
│  │                SSL + Security Headers                  │
│  └─────────────────┬───────────────────────────────────────┤
│                    │                                       │
│                    ▼                                       │
│  ┌─────────────────────────────────────────────────────────┤
│  │            🐳 Docker Container                          │
│  │  ┌─────────────────┐    ┌─────────────────┐            │
│  │  │  🌐 Frontend    │    │  🦀 Backend     │            │
│  │  │  Static Build   │    │  Rust Release   │            │
│  │  │  Nginx Serve    │    │  Port 3001      │            │
│  │  └─────────────────┘    └─────────────────┘            │
│  │           │                       │                    │
│  │           │                       │                    │
│  │           ▼                       ▼                    │
│  │  ┌─────────────────┐    ┌─────────────────┐            │
│  │  │  🔀 Nginx       │    │  🗄️ Redis       │            │
│  │  │  Port 80        │    │  Port 6379      │            │
│  │  │  Internal Proxy │    │  Cache Prod     │            │
│  │  └─────────────────┘    └─────────────────┘            │
│  └─────────────────────────────────────────────────────────┤
└─────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────┐
│                ☁️ Supabase PostgreSQL                       │
│                Production Database                         │
└─────────────────────────────────────────────────────────────┘

Fluxo de Dados (Produção):
1. Internet → Nginx Host (SSL) → Docker Nginx → Frontend/Backend
2. API calls → /api → Backend → Supabase
3. Static files → Frontend build → Nginx serve
4. Cache → Redis → Backend optimization
```

### 🔄 Fluxo de Deploy (Produção)

```bash
# 1. Backup antes do deploy
/opt/scripts/backup-blueprint.sh

# 2. Pull das mudanças
cd /opt/blueprint-blog
git pull origin main

# 3. Build das novas imagens
docker-compose -f docker-compose-prod.yml build --no-cache

# 4. Deploy com zero downtime
docker-compose -f docker-compose-prod.yml up -d

# 5. Verificar saúde
sleep 30
curl -f https://blueprintblog.tech/health || echo "Deploy failed!"

# 6. Verificar logs
docker-compose -f docker-compose-prod.yml logs -f --tail=50

# 7. Limpar imagens antigas
docker image prune -f
```

### 🔧 Comandos Úteis (Produção)

```bash
# Status dos serviços
docker-compose -f docker-compose-prod.yml ps
systemctl status nginx
systemctl status docker

# Logs em tempo real
docker-compose -f docker-compose-prod.yml logs -f
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Monitoramento
docker stats --no-stream
df -h
free -h
htop

# Backup manual
tar -czf /backup/blueprint-$(date +%Y%m%d).tar.gz /opt/blueprint-blog/

# Restore (emergência)
cd /opt/blueprint-blog
git checkout HEAD~1  # Voltar 1 commit
docker-compose -f docker-compose-prod.yml up -d

# SSL renewal
sudo certbot renew --dry-run
sudo certbot renew

# Limpar logs antigos
sudo journalctl --vacuum-time=7d
sudo find /var/log -name "*.log" -type f -mtime +30 -delete
```

### 📊 Monitoramento e Alertas

```bash
# Script de health check
#!/bin/bash
# /opt/scripts/health-check.sh
if ! curl -f https://blueprintblog.tech/health > /dev/null 2>&1; then
    echo "Site down! $(date)" >> /var/log/blueprint-alerts.log
    # Enviar alerta (email, slack, etc.)
fi

# Crontab para monitoramento
sudo crontab -e
# Health check a cada 5 minutos
*/5 * * * * /opt/scripts/health-check.sh

# Backup diário às 2h
0 2 * * * /opt/scripts/backup-blueprint.sh

# SSL renewal check semanal
0 3 * * 0 /usr/bin/certbot renew --quiet

# Limpar logs semanalmente
0 4 * * 0 /opt/scripts/cleanup-logs.sh
```

---

## 🔄 WORKFLOW COMPLETO

### 🔧 Desenvolvimento → Produção

```bash
# 1. Desenvolvimento local
git checkout -b feature/nova-funcionalidade
# ... desenvolver ...
npm test && cargo test
git add . && git commit -m "feat: nova funcionalidade"
git push origin feature/nova-funcionalidade

# 2. Pull Request e Review
# ... review no GitHub ...
git checkout main
git pull origin main

# 3. Deploy para produção
ssh <EMAIL>
cd /opt/blueprint-blog
git pull origin main
docker-compose -f docker-compose-prod.yml build
docker-compose -f docker-compose-prod.yml up -d

# 4. Verificar deploy
curl -I https://blueprintblog.tech/health
docker-compose -f docker-compose-prod.yml logs -f --tail=20
```

### 🚨 Rollback de Emergência

```bash
# Se algo der errado em produção
cd /opt/blueprint-blog

# Opção 1: Voltar 1 commit
git checkout HEAD~1
docker-compose -f docker-compose-prod.yml up -d

# Opção 2: Usar backup
tar -xzf /backup/blueprint-$(date -d yesterday +%Y%m%d).tar.gz
docker-compose -f docker-compose-prod.yml up -d

# Opção 3: Usar imagem anterior
docker-compose -f docker-compose-prod.yml down
docker run -d --name temp-backend blueprint-backend:previous
# ... configurar temporariamente ...
```

### 📈 Otimizações de Performance

```bash
# Produção - Configurações otimizadas
# docker-compose-prod.yml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      - RUST_LOG=info  # Menos verbose
      - LOG_JSON=true  # Logs estruturados

  frontend:
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  nginx:
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 128M
```

---

**🎯 Este fluxo garante:**

- ✅ **Desenvolvimento** ágil e isolado
- ✅ **Produção** estável e monitorada
- ✅ **Deploy** seguro com rollback
- ✅ **Monitoramento** 24/7
- ✅ **Backup** automático
- ✅ **SSL** e segurança
- ✅ **Performance** otimizada
