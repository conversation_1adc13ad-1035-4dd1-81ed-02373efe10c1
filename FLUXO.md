- Fluxo de conexão:

+-------------------+ +---------------------+ +-------------------+
| Frontend (Vite)| <---> | NGINX (proxy) | <---> | Backend (Rust) |
+-------------------+ +---------------------+ +-------------------+
|
v
+-------------------+
| Supabase (DB) |
+-------------------+

- Esquema de diretorios:
  /blueprint-blog
  ├── /frontend
  │ ├── Dockerfile
  │ ├── nginx.conf (frontend serve only in prod, or via NGINX upstream)
  │ └── .env.production
  │
  ├── /backend
  │ ├── Dockerfile
  │ ├── .env.production
  │
  ├── /nginx
  │ └── nginx.conf <-- Este é o que gerencia TUDO (Front + Backend)
  │
  ├── docker-compose.yml
  ├── .env (ou .env.build central opcional)
  └── Makefile

- Fluxo de comunição local:
  Frontend → http://localhost:5173 (Vite dev server)
  Backend → http://localhost:3001 (Rust)

- Fluxo em produção:
  Frontend → NGINX → serve o frontend static /usr/share/nginx/html
  Backend → NGINX → proxy_pass /api → http://backend:3001/api

- Exemplo em produção:
  https://blueprintblog.tech/ → Frontend (React static)
  https://blueprintblog.tech/api/posts → Backend Rust API
