version: '3.8'

services:
  # Nginx Único - Serve Frontend + Proxy Backend
  nginx:
    build:
      context: .
      dockerfile: nginx.Dockerfile
    ports:
      - '80:80'
      - '443:443'
    networks:
      - app-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

  # Frontend React + TypeScript (Vite)
  frontend:
    build: ./frontend
    networks:
      - app-network
    volumes:
      - frontend_dist:/app/dist
    environment:
      - NODE_ENV=production
      - VITE_API_URL=http://localhost/api
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:5173 || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend Rust
  backend:
    build: ./backend
    # 🔒 SEM exposição direta - apenas via nginx
    # ports: [] # Removido para segurança
    networks:
      - app-network # Rede única simplificada
    environment:
      - DATABASE_URL=postgresql://postgres.nbzovkwvrqlxqpdpkvql:<EMAIL>:5432/postgres
      - REDIS_URL=redis://redis:6379
      - RUST_LOG=info
      - LOG_LEVEL=info
      - LOG_CONSOLE=true
      - LOG_FILE=true
      - LOG_JSON=true
      - LOG_DIR=./logs
      - LOG_MAX_FILES=30
      - LOG_MAX_AGE_DAYS=30
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    # depends_on:
    #   - redis
    # 🔧 Limites de recursos para AWS (evitar 100% CPU)
    deploy:
      resources:
        limits:
          cpus: '0.5' # Máximo 50% de 1 CPU core
          memory: 512M # Máximo 512MB RAM
        reservations:
          cpus: '0.25' # Reserva mínima 25% de 1 CPU core
          memory: 256M # Reserva mínima 256MB RAM
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:3001/health || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL local removido - usando apenas Supabase

  # Redis Cache - DESABILITADO TEMPORARIAMENTE
  # redis:
  #   image: redis:7-alpine
  #   networks:
  #     - app-network
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped

# 🎯 Monitoramento removido temporariamente - foco no core: frontend + backend
# prometheus e grafana serão adicionados depois que o sistema estiver estável

# Rede única simplificada
networks:
  app-network:
    driver: bridge

# Volumes
volumes:
  frontend_dist:
  redis_data:
  backend_logs:
  backend_uploads:
