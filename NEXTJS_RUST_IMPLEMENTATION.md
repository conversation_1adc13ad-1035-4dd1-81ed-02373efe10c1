# 🚀 Blueprint Blog - Implementação Next.js + Rust

## 📋 **Guia Completo de Implementação**

### **🎯 Objetivo**

Implementar frontend Next.js 15 + TypeScript conectado ao backend Rust existente, mantendo o design cyberpunk e otimizando a experiência do usuário.

---

## 🏗️ **Arquitetura Final**

```
┌─────────────────────────────────────────────────────────────┐
│                    🌐 Cliente (Browser)                     │
├─────────────────────────────────────────────────────────────┤
│  Next.js 15 + TypeScript + Tailwind 4                     │
│  ✅ SSR/SSG para SEO perfeito                              │
│  ✅ Compile-time error detection                           │
│  ✅ Otimização automática de imagens/fonts                 │
└─────────────────┬───────────────────────────────────────────┘
                  │ API Calls
                  ▼
┌─────────────────────────────────────────────────────────────┐
│         🐳 Docker Container (blueprint-blog)               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  🔀 Nginx       │    │  ⚡ Next.js      │                │
│  │  Port 3002      │────│  Port 3000      │                │
│  │  Proxy Reverso  │    │  SSR + Client   │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                         │
│           │                       │ API Requests            │
│           │                       ▼                         │
│           ▼              ┌─────────────────┐                │
│  ┌─────────────────┐────│  🦀 Backend     │                │
│  │  🗄️ Redis       │    │  Port 3001      │                │
│  │  Port 6379      │    │  Rust + Axum    │                │
│  │  Cache Layer    │    │  API Routes     │                │
│  └─────────────────┘    └─────────────────┘                │
│                                   │                         │
└───────────────────────────────────┼─────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────┐
│                ☁️ Supabase PostgreSQL                       │
│                Session Pooler                              │
└─────────────────────────────────────────────────────────────┘
```

---

## 📦 **Stack Tecnológica**

### **🎨 Frontend (Next.js)**

```json
{
  "framework": "Next.js 15.3.3",
  "language": "TypeScript 5.x",
  "styling": "Tailwind CSS 4.x",
  "animations": "Motion (Framer Motion)",
  "icons": "Lucide React",
  "bundler": "Turbopack",
  "deployment": "Docker + Nginx"
}
```

### **🦀 Backend (Rust) - Existente**

```toml
[dependencies]
axum = "0.7"
tokio = "1.0"
sqlx = "0.8"
serde = "1.0"
tower = "0.5"
tower-http = "0.6"
```

### **🗄️ Database - Existente**

```
Supabase PostgreSQL
├── Session Pooler (IPv6 compatible)
├── Real-time subscriptions
└── Row Level Security (RLS)
```

---

## 🎨 **Design System Cyberpunk**

### **🌈 Paleta de Cores**

```css
/* Dark Theme (Principal) */
--background: #0a0a0a; /* Preto profundo */
--foreground: #ffffff; /* Branco puro */
--card: #111111; /* Cinza muito escuro */
--border: #222222; /* Bordas sutis */
--primary: #00d4ff; /* Azul neon */
--secondary: #00ff88; /* Verde neon */
--accent: #8b5cf6; /* Roxo cyber */

/* Light Theme (Alternativo) */
--background: #f1f3f4; /* Cinza muito claro */
--foreground: #1f2937; /* Cinza escuro */
--card: #fefefe; /* Branco quase puro */
--border: #e5e7eb; /* Bordas suaves */
--primary: #2563eb; /* Azul padrão */
--secondary: #059669; /* Verde padrão */
```

### **🔤 Tipografia**

```css
/* Fonts Cyberpunk */
--font-primary: 'Space Grotesk', sans-serif;  /* UI e títulos */
--font-mono: 'JetBrains Mono', monospace;     /* Código e detalhes */

/* Hierarquia */
h1: text-4xl md:text-6xl      /* 2.25rem - 3.75rem */
h2: text-3xl md:text-4xl      /* 1.875rem - 2.25rem */
h3: text-2xl md:text-3xl      /* 1.5rem - 1.875rem */
body: text-base               /* 1rem */
small: text-sm                /* 0.875rem */
```

### **✨ Efeitos Visuais**

```css
/* Background Pattern */
.dark body {
  background-image: radial-gradient(
    circle at 1px 1px,
    rgba(0, 212, 255, 0.15) 1px,
    transparent 0
  );
  background-size: 20px 20px;
}

/* Text Gradient */
.text-gradient {
  background: linear-gradient(135deg, #00d4ff, #00ff88);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Glow Effects */
.glow-text {
  text-shadow: 0 0 10px currentColor;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px currentColor;
  }
  to {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}
```

---

## 🧩 **Estrutura de Componentes**

### **📁 Organização de Arquivos**

```
frontend-nextjs/src/
├── app/                      # App Router (Next.js 13+)
│   ├── globals.css          # Estilos globais + Tailwind
│   ├── layout.tsx           # Layout raiz
│   ├── page.tsx             # Home page
│   ├── blog/
│   │   ├── page.tsx         # Lista de posts
│   │   └── [slug]/
│   │       └── page.tsx     # Post individual
│   ├── projects/
│   │   └── page.tsx         # Lista de projetos
│   ├── about/
│   │   └── page.tsx         # Sobre
│   ├── contact/
│   │   └── page.tsx         # Contato
│   └── api/                 # API Routes (opcional)
│       └── revalidate/
│           └── route.ts     # Revalidação de cache
├── components/
│   ├── layout/              # Componentes de layout
│   │   ├── Navbar.tsx       # ✅ Implementado
│   │   ├── Footer.tsx       # 🔄 A implementar
│   │   └── Sidebar.tsx      # 🔄 A implementar
│   ├── ui/                  # Componentes de interface
│   │   ├── ThemeToggle.tsx  # ✅ Implementado
│   │   ├── Button.tsx       # 🔄 A implementar
│   │   ├── Card.tsx         # 🔄 A implementar
│   │   ├── Input.tsx        # 🔄 A implementar
│   │   ├── Modal.tsx        # 🔄 A implementar
│   │   └── LoadingSpinner.tsx # 🔄 A implementar
│   ├── blog/                # Componentes do blog
│   │   ├── PostCard.tsx     # 🔄 A implementar
│   │   ├── PostList.tsx     # 🔄 A implementar
│   │   ├── PostContent.tsx  # 🔄 A implementar
│   │   └── MarkdownRenderer.tsx # 🔄 A implementar
│   ├── projects/            # Componentes de projetos
│   │   ├── ProjectCard.tsx  # 🔄 A implementar
│   │   └── ProjectList.tsx  # 🔄 A implementar
│   └── common/              # Componentes comuns
│       ├── SEO.tsx          # 🔄 A implementar
│       ├── ErrorBoundary.tsx # 🔄 A implementar
│       └── OptimizedImage.tsx # 🔄 A implementar
├── lib/                     # Bibliotecas e utilitários
│   ├── api.ts              # Cliente API para Rust backend
│   ├── types.ts            # Tipos TypeScript
│   ├── utils.ts            # Funções utilitárias
│   └── constants.ts        # Constantes da aplicação
├── hooks/                   # Custom hooks
│   ├── useApi.ts           # Hook para API calls
│   ├── usePosts.ts         # Hook para posts
│   └── useProjects.ts      # Hook para projetos
└── styles/                  # Estilos adicionais
    └── components.css       # Estilos específicos
```

---

## 🔌 **Integração com Backend Rust**

### **🌐 API Client**

```typescript
// lib/api.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }
}

export const apiClient = new ApiClient();
```

### **📝 Tipos TypeScript**

```typescript
// lib/types.ts
export interface Post {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image?: string;
  published: boolean;
  created_at: string;
  updated_at: string;
  tags: string[];
  reading_time: number;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  image_url?: string;
  github_url?: string;
  demo_url?: string;
  technologies: string[];
  status: 'active' | 'completed' | 'archived';
  created_at: string;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}
```

---

## 🎭 **Implementação de Animações**

### **🎨 Motion Components**

```typescript
// components/ui/AnimatedCard.tsx
import { motion } from 'motion/react';

interface AnimatedCardProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
}

export function AnimatedCard({
  children,
  delay = 0,
  className = '',
}: AnimatedCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.5,
        delay,
        type: 'spring',
        stiffness: 100,
        damping: 20,
      }}
      whileHover={{
        scale: 1.02,
        transition: { duration: 0.2 },
      }}
      className={`card ${className}`}>
      {children}
    </motion.div>
  );
}
```

### **🎯 Page Transitions**

```typescript
// components/common/PageTransition.tsx
import { motion, AnimatePresence } from 'motion/react';
import { usePathname } from 'next/navigation';

export function PageTransition({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pathname}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className="min-h-screen">
        {children}
      </motion.div>
    </AnimatePresence>
  );
}
```

---

## 🔧 **Configuração de Desenvolvimento**

### **🐳 Docker Setup**

```dockerfile
# Dockerfile.nextjs
FROM node:20-alpine AS base

# Dependencies
FROM base AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Builder
FROM base AS builder
WORKDIR /app
COPY . .
COPY --from=deps /app/node_modules ./node_modules
RUN npm run build

# Runner
FROM base AS runner
WORKDIR /app
ENV NODE_ENV=production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT=3000

CMD ["node", "server.js"]
```

### **🔄 Docker Compose**

```yaml
# docker-compose.nextjs.yml
version: '3.8'

services:
  frontend-nextjs:
    build:
      context: ./frontend-nextjs
      dockerfile: Dockerfile
    ports:
      - '3000:3000'
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3001
    depends_on:
      - backend
    networks:
      - blueprint-network

  backend:
    # Usar o backend Rust existente
    extends:
      file: ../docker-compose.yml
      service: backend

  nginx:
    image: nginx:alpine
    ports:
      - '3002:80'
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend-nextjs
      - backend
    networks:
      - blueprint-network

networks:
  blueprint-network:
    driver: bridge
```

---

## 📊 **Performance e SEO**

### **⚡ Otimizações Next.js**

```typescript
// next.config.ts
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // Experimental features
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },

  // Image optimization
  images: {
    domains: ['supabase.co', 'your-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },

  // Compression
  compress: true,

  // Static generation
  output: 'standalone',

  // Headers for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
```

### **🔍 SEO Configuration**

```typescript
// app/layout.tsx
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: {
    template: '%s | Blueprint Blog',
    default: 'Blueprint Blog - Desenvolvimento e Tecnologia',
  },
  description:
    'Blog sobre desenvolvimento, tecnologia e insights de programação com foco em Rust, TypeScript e arquitetura de software.',
  keywords: [
    'desenvolvimento',
    'rust',
    'typescript',
    'nextjs',
    'blog',
    'tecnologia',
  ],
  authors: [{ name: 'Blueprint Blog' }],
  creator: 'Blueprint Blog',
  openGraph: {
    type: 'website',
    locale: 'pt_BR',
    url: 'https://blueprintblog.duckdns.org',
    siteName: 'Blueprint Blog',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Blueprint Blog',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Blueprint Blog',
    description: 'Blog sobre desenvolvimento e tecnologia',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};
```

---

## 🚀 **Próximos Passos de Implementação**

### **Fase 1: Componentes Base (Semana 1)**

- [ ] Footer cyberpunk
- [ ] Button component com variantes
- [ ] Card component responsivo
- [ ] Input/Form components
- [ ] Modal/Dialog components
- [ ] LoadingSpinner animado

### **Fase 2: Blog Components (Semana 2)**

- [ ] PostCard com preview
- [ ] PostList com paginação
- [ ] PostContent com markdown
- [ ] MarkdownRenderer otimizado
- [ ] SEO por página
- [ ] Breadcrumbs navigation

### **Fase 3: Pages & Features (Semana 3)**

- [ ] Blog listing page
- [ ] Individual post pages
- [ ] Projects showcase
- [ ] About page
- [ ] Contact form
- [ ] Search functionality

### **Fase 4: Otimizações (Semana 4)**

- [ ] Image optimization
- [ ] Font optimization
- [ ] Bundle analysis
- [ ] Performance monitoring
- [ ] Error tracking
- [ ] Analytics integration

---

**🎯 Objetivo Final:** Frontend Next.js totalmente funcional, otimizado e conectado ao backend Rust, mantendo o design cyberpunk original com performance superior e SEO perfeito.

---

## 🛠️ **Implementação Detalhada dos Componentes**

### **🎨 UI Components**

#### **Button Component**

```typescript
// components/ui/Button.tsx
import { motion } from 'motion/react';
import { forwardRef } from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      loading = false,
      children,
      className = '',
      ...props
    },
    ref
  ) => {
    const baseClasses =
      'inline-flex items-center justify-center rounded-md font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

    const variants = {
      primary:
        'bg-gradient-to-r from-primary/80 to-primary text-primary-foreground hover:from-primary hover:to-primary/80 focus:ring-primary',
      secondary:
        'bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-secondary',
      ghost: 'bg-transparent text-foreground hover:bg-muted focus:ring-muted',
      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    };

    const sizes = {
      sm: 'h-8 px-3 text-sm',
      md: 'h-10 px-4 text-base',
      lg: 'h-12 px-6 text-lg',
    };

    return (
      <motion.button
        ref={ref}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
        disabled={loading}
        {...props}>
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {children}
      </motion.button>
    );
  }
);

Button.displayName = 'Button';
```

#### **Card Component**

```typescript
// components/ui/Card.tsx
import { motion } from 'motion/react';
import { forwardRef } from 'react';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined';
  hover?: boolean;
  children: React.ReactNode;
}

export const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    { variant = 'default', hover = true, children, className = '', ...props },
    ref
  ) => {
    const baseClasses = 'rounded-lg transition-all duration-300';

    const variants = {
      default: 'bg-card border border-border',
      elevated: 'bg-card shadow-lg border border-border',
      outlined: 'bg-transparent border-2 border-border',
    };

    const hoverClasses = hover
      ? 'hover:shadow-xl hover:border-primary/50 dark:hover:border-primary/50'
      : '';

    const CardComponent = hover ? motion.div : 'div';
    const motionProps = hover
      ? {
          whileHover: { y: -2 },
          transition: { duration: 0.2 },
        }
      : {};

    return (
      <CardComponent
        ref={ref}
        className={`${baseClasses} ${variants[variant]} ${hoverClasses} ${className}`}
        {...motionProps}
        {...props}>
        {children}
      </CardComponent>
    );
  }
);

Card.displayName = 'Card';

export const CardHeader = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className = '', ...props }, ref) => (
  <div ref={ref} className={`p-6 pb-0 ${className}`} {...props} />
));

export const CardContent = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className = '', ...props }, ref) => (
  <div ref={ref} className={`p-6 ${className}`} {...props} />
));

export const CardFooter = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className = '', ...props }, ref) => (
  <div ref={ref} className={`p-6 pt-0 ${className}`} {...props} />
));
```

#### **Input Component**

```typescript
// components/ui/Input.tsx
import { forwardRef } from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, icon, className = '', ...props }, ref) => {
    return (
      <div className="space-y-2">
        {label && (
          <label className="text-sm font-medium text-foreground">{label}</label>
        )}
        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              {icon}
            </div>
          )}
          <input
            ref={ref}
            className={`
              w-full px-3 py-2 bg-background border border-border rounded-md
              focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
              disabled:opacity-50 disabled:cursor-not-allowed
              transition-all duration-300
              ${icon ? 'pl-10' : ''}
              ${error ? 'border-red-500 focus:ring-red-500' : ''}
              ${className}
            `}
            {...props}
          />
        </div>
        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    );
  }
);

Input.displayName = 'Input';
```

### **📝 Blog Components**

#### **PostCard Component**

```typescript
// components/blog/PostCard.tsx
import { motion } from 'motion/react';
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/Card';
import { formatDate } from '@/lib/utils';
import type { Post } from '@/lib/types';

interface PostCardProps {
  post: Post;
  index?: number;
}

export function PostCard({ post, index = 0 }: PostCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group">
      <Link href={`/blog/${post.slug}`}>
        <Card hover className="h-full overflow-hidden">
          {post.featured_image && (
            <div className="aspect-video overflow-hidden">
              <Image
                src={post.featured_image}
                alt={post.title}
                width={400}
                height={225}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
          )}

          <CardContent className="space-y-3">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <time dateTime={post.created_at}>
                {formatDate(post.created_at)}
              </time>
              <span>•</span>
              <span>{post.reading_time} min read</span>
            </div>

            <h3 className="text-xl font-semibold text-gradient group-hover:glow-text transition-all duration-300">
              {post.title}
            </h3>

            <p className="text-muted-foreground line-clamp-3">{post.excerpt}</p>

            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-full">
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </Link>
    </motion.div>
  );
}
```

#### **MarkdownRenderer Component**

```typescript
// components/blog/MarkdownRenderer.tsx
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeSlug from 'rehype-slug';
import 'highlight.js/styles/github-dark.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export function MarkdownRenderer({
  content,
  className = '',
}: MarkdownRendererProps) {
  return (
    <div className={`prose prose-lg dark:prose-invert max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight, rehypeSlug]}
        components={{
          // Custom components for better styling
          h1: ({ children }) => (
            <h1 className="text-4xl font-bold text-gradient mb-6">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-3xl font-semibold text-foreground mt-8 mb-4">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-2xl font-medium text-foreground mt-6 mb-3">
              {children}
            </h3>
          ),
          p: ({ children }) => (
            <p className="text-foreground leading-relaxed mb-4">{children}</p>
          ),
          code: ({ inline, children, ...props }) =>
            inline ? (
              <code
                className="bg-muted px-1 py-0.5 rounded text-sm font-mono"
                {...props}>
                {children}
              </code>
            ) : (
              <code
                className="block bg-card p-4 rounded-lg overflow-x-auto"
                {...props}>
                {children}
              </code>
            ),
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-primary pl-4 italic text-muted-foreground my-6">
              {children}
            </blockquote>
          ),
          a: ({ href, children }) => (
            <a
              href={href}
              className="text-primary hover:text-primary/80 underline transition-colors"
              target={href?.startsWith('http') ? '_blank' : undefined}
              rel={
                href?.startsWith('http') ? 'noopener noreferrer' : undefined
              }>
              {children}
            </a>
          ),
        }}>
        {content}
      </ReactMarkdown>
    </div>
  );
}
```

### **🔧 Custom Hooks**

#### **useApi Hook**

```typescript
// hooks/useApi.ts
import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';

interface UseApiOptions {
  immediate?: boolean;
}

export function useApi<T>(
  endpoint: string,
  options: UseApiOptions = { immediate: true }
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiClient.get<T>(endpoint);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (options.immediate) {
      execute();
    }
  }, [endpoint, options.immediate]);

  return { data, loading, error, refetch: execute };
}
```

#### **usePosts Hook**

```typescript
// hooks/usePosts.ts
import { useApi } from './useApi';
import type { Post, PaginatedResponse } from '@/lib/types';

interface UsePostsOptions {
  page?: number;
  limit?: number;
  published?: boolean;
}

export function usePosts(options: UsePostsOptions = {}) {
  const { page = 1, limit = 10, published = true } = options;

  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    published: published.toString(),
  });

  const { data, loading, error, refetch } = useApi<PaginatedResponse<Post>>(
    `/api/posts?${queryParams}`
  );

  return {
    posts: data?.data || [],
    pagination: data?.pagination,
    loading,
    error,
    refetch,
  };
}

export function usePost(slug: string) {
  const { data, loading, error, refetch } = useApi<Post>(`/api/posts/${slug}`);

  return {
    post: data,
    loading,
    error,
    refetch,
  };
}
```

### **🎯 Page Components**

#### **Blog List Page**

```typescript
// app/blog/page.tsx
import { Metadata } from 'next';
import { PostCard } from '@/components/blog/PostCard';
import { Button } from '@/components/ui/Button';
import { apiClient } from '@/lib/api';
import type { Post, PaginatedResponse } from '@/lib/types';

export const metadata: Metadata = {
  title: 'Blog',
  description: 'Artigos sobre desenvolvimento, tecnologia e programação',
};

async function getPosts(page: number = 1): Promise<PaginatedResponse<Post>> {
  return apiClient.get(`/api/posts?page=${page}&limit=12&published=true`);
}

export default async function BlogPage({
  searchParams,
}: {
  searchParams: { page?: string };
}) {
  const page = Number(searchParams.page) || 1;
  const { data: posts, pagination } = await getPosts(page);

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="py-12 text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-gradient mb-4">
          Blog
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Artigos sobre desenvolvimento, tecnologia e insights de programação
        </p>
      </section>

      {/* Posts Grid */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {posts.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post, index) => (
                <PostCard key={post.id} post={post} index={index} />
              ))}
            </div>

            {/* Pagination */}
            {pagination && pagination.total_pages > 1 && (
              <div className="flex justify-center items-center gap-4 mt-12">
                {pagination.page > 1 && (
                  <Button variant="ghost" asChild>
                    <Link href={`/blog?page=${pagination.page - 1}`}>
                      Anterior
                    </Link>
                  </Button>
                )}

                <span className="text-muted-foreground">
                  Página {pagination.page} de {pagination.total_pages}
                </span>

                {pagination.page < pagination.total_pages && (
                  <Button variant="ghost" asChild>
                    <Link href={`/blog?page=${pagination.page + 1}`}>
                      Próxima
                    </Link>
                  </Button>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-2xl font-semibold text-muted-foreground mb-4">
              Nenhum post encontrado
            </h3>
            <p className="text-muted-foreground">
              Volte em breve para ver novos conteúdos!
            </p>
          </div>
        )}
      </section>
    </div>
  );
}
```

---

## 🚀 **Deploy e Produção**

### **🐳 Docker Configuration**

```yaml
# docker-compose.nextjs.yml
version: '3.8'

services:
  frontend-nextjs:
    build:
      context: ./frontend-nextjs
      dockerfile: Dockerfile
    container_name: blueprint-nextjs
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:3001
      - NEXT_PUBLIC_SITE_URL=https://blueprintblog.duckdns.org
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - blueprint-network

  nginx-nextjs:
    image: nginx:alpine
    container_name: blueprint-nginx-nextjs
    ports:
      - '3002:80'
    volumes:
      - ./nginx-nextjs.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend-nextjs
    restart: unless-stopped
    networks:
      - blueprint-network

networks:
  blueprint-network:
    external: true
```

### **🔧 Nginx Configuration**

```nginx
# nginx-nextjs.conf
events {
    worker_connections 1024;
}

http {
    upstream nextjs {
        server frontend-nextjs:3000;
    }

    upstream backend {
        server backend:3001;
    }

    server {
        listen 80;
        server_name localhost;

        # Gzip compression
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

        # Frontend routes
        location / {
            proxy_pass http://nextjs;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # API routes
        location /api/ {
            proxy_pass http://backend/api/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files caching
        location /_next/static/ {
            proxy_pass http://nextjs;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

---

**🎯 Resultado Final:** Sistema completo Next.js + Rust com performance otimizada, SEO perfeito e design cyberpunk mantido, pronto para produção.
