# 🌐 Configuração do Domínio blueprintblog.tech

Este documento descreve a configuração e migração para o domínio profissional `blueprintblog.tech`.

## 📋 Informações do Domínio

- **Domínio:** blueprintblog.tech
- **Tipo:** Domínio .tech profissional
- **SSL:** Certificado Let's Encrypt (renovação automática)
- **CDN:** Cloudflare (opcional)
- **DNS:** Configuração A/CNAME para servidor

## 🔧 Configurações Atualizadas

### Backend (.env)
```env
CORS_ORIGIN=https://blueprintblog.tech
```

### Frontend (.env.production)
```env
VITE_API_URL=https://blueprintblog.tech/api
VITE_APP_URL=https://blueprintblog.tech
```

### Docker Compose
```yaml
environment:
  - VITE_API_URL=https://blueprintblog.tech/api
```

## 🚀 Deploy para Produção

### 1. Build e Deploy
```bash
# Build completo
make build

# Deploy para produção
make deploy-prod

# Verificar status
make status
```

### 2. Configuração SSL
```bash
# Gerar certificado Let's Encrypt
certbot --nginx -d blueprintblog.tech

# Renovação automática
crontab -e
# Adicionar: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Configuração DNS
```
# Registros DNS necessários:
A     blueprintblog.tech     -> IP_DO_SERVIDOR
CNAME www.blueprintblog.tech -> blueprintblog.tech
```

## 🔍 Verificações

### Conectividade
```bash
# DNS Resolution
nslookup blueprintblog.tech

# HTTP/HTTPS
curl -I https://blueprintblog.tech/health

# SSL Certificate
openssl s_client -connect blueprintblog.tech:443 -servername blueprintblog.tech
```

### APIs
```bash
# Health Check
curl -X GET https://blueprintblog.tech/api/health

# Posts API
curl -X GET https://blueprintblog.tech/api/posts

# Auth API
curl -X POST https://blueprintblog.tech/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}'
```

## 📊 Monitoramento

### Logs
```bash
# Nginx logs
docker-compose logs -f nginx

# Backend logs
docker-compose logs -f backend

# Sistema
journalctl -u docker -f
```

### Performance
```bash
# Lighthouse
lighthouse https://blueprintblog.tech

# GTmetrix
curl -X POST "https://gtmetrix.com/api/0.1/test" \
  -u "email:api_key" \
  -d "url=https://blueprintblog.tech"
```

## 🔒 Segurança

### Headers de Segurança
```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

### Rate Limiting
```nginx
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
```

## 🎯 SEO e Meta Tags

### Meta Tags Atualizadas
```html
<meta property="og:url" content="https://blueprintblog.tech" />
<meta property="og:site_name" content="Blueprint Blog" />
<link rel="canonical" href="https://blueprintblog.tech" />
```

### Sitemap
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://blueprintblog.tech/</loc>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>
```

## 📈 Analytics

### Google Analytics
```javascript
// gtag.js
gtag('config', 'GA_MEASUREMENT_ID', {
  page_title: 'Blueprint Blog',
  page_location: 'https://blueprintblog.tech'
});
```

### Search Console
- Adicionar propriedade: https://blueprintblog.tech
- Verificar via DNS TXT record
- Submeter sitemap: https://blueprintblog.tech/sitemap.xml

## 🔄 Migração Completa

### Checklist
- [x] Configurações backend atualizadas
- [x] Configurações frontend atualizadas  
- [x] Docker Compose atualizado
- [x] Documentação atualizada
- [x] Comandos úteis atualizados
- [ ] DNS configurado
- [ ] SSL configurado
- [ ] Deploy realizado
- [ ] Testes de conectividade
- [ ] SEO configurado
- [ ] Analytics configurado

---

*📝 Documento criado durante a migração para blueprintblog.tech*
*🗓️ Data: Janeiro 2025*
