# 🛠️ Comandos Ú<PERSON>is - Blueprint Blog

Este documento contém comandos úteis para desenvolvimento, manutenção e troubleshooting do projeto.

**🌐 Domínio:** https://blueprintblog.tech

## 📋 Índice

- [Git & Versionamento](#git--versionamento)
- [Node.js & Frontend](#nodejs--frontend)
- [Rust & Backend](#rust--backend)
- [TypeScript](#typescript)
- [Docker](#docker)
- [Sistema & Arquivos](#sistema--arquivos)
- [Troubleshooting](#troubleshooting)

---

## 🔄 Git & Versionamento

### Verificar status e histórico

```bash
# Status atual
git status

# Histórico de commits
git log --oneline -5

# Ver mudanças no último commit
git show --name-status HEAD

# Ver arquivos rastreados em um diretório
git ls-files backend/

# Restaurar arquivos de um commit
git checkout HEAD -- backend/
```

### Commits e branches

```bash
# Adicionar todos os arquivos
git add .

# Commit com mensagem
git commit -m "feat: Complete TypeScript migration to 100%"

# Ver diferenças
git diff
git diff --cached
```

---

## 📦 Node.js & Frontend

### Gerenciamento de dependências

```bash
# Instalar dependências
npm install

# Limpar cache
npm cache clean --force

# Verificar scripts disponíveis
npm run

# Remover node_modules e reinstalar
Remove-Item -Recurse -Force node_modules  # PowerShell
rm -rf node_modules                        # Bash
npm install
```

### Build e desenvolvimento

```bash
# Servidor de desenvolvimento
npm run dev

# Build de produção
npm run build

# Preview do build
npm run preview

# Verificar bundle size
npm run build -- --analyze
```

### Verificar estrutura de arquivos

```bash
# Contar arquivos por extensão
Get-ChildItem -Path src -Recurse -Include *.js,*.jsx | Measure-Object | Select-Object Count
Get-ChildItem -Path src -Recurse -Include *.ts,*.tsx | Measure-Object | Select-Object Count

# Listar arquivos específicos
Get-ChildItem -Path src -Recurse -Include *.js,*.jsx | Select-Object Name,Directory
```

---

## 🦀 Rust & Backend

### Instalação do Rust

```bash
# Windows (PowerShell)
Invoke-WebRequest -Uri "https://win.rustup.rs/x86_64" -OutFile "rustup-init.exe"
.\rustup-init.exe --default-toolchain stable --profile default --target x86_64-pc-windows-msvc -y

# Linux/WSL
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### Comandos Cargo

```bash
# Verificar versão
cargo --version
rustc --version

# Compilar projeto
cargo build

# Executar projeto
cargo run

# Executar com logs
RUST_LOG=debug cargo run

# Verificar código
cargo check

# Executar testes
cargo test

# Atualizar dependências
cargo update
```

---

## 🔍 TypeScript

### Verificação de tipos

```bash
# Verificar erros sem compilar
npx tsc --noEmit

# Verificar arquivo específico
npx tsc --noEmit src/components/Component.tsx

# Gerar arquivos de declaração
npx tsc --declaration --emitDeclarationOnly
```

### Configuração

```bash
# Verificar configuração
npx tsc --showConfig

# Inicializar tsconfig.json
npx tsc --init
```

---

## 🐳 Docker & Docker Compose

### 📋 Comandos básicos Docker

```bash
# Verificar versão e informações
docker --version
docker info
docker system info

# Listar containers
docker ps                    # Containers rodando
docker ps -a                 # Todos os containers
docker ps -q                 # Apenas IDs
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Listar imagens
docker images
docker images -a             # Incluir intermediárias
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
```

### 🏗️ Build e Gestão de Imagens

```bash
# Construir imagens
docker build -t blueprint-backend ./backend
docker build -t blueprint-frontend ./frontend
docker build --no-cache -t app:latest .
docker build --target production -t app:prod .

# Construir com argumentos
docker build --build-arg NODE_ENV=production -t app .

# Remover imagens
docker rmi image_name
docker rmi $(docker images -q)           # Todas as imagens
docker image prune                       # Imagens não utilizadas
docker image prune -a                    # Todas as imagens órfãs
```

### 🚀 Executar Containers

```bash
# Executar containers
docker run -p 3001:3001 blueprint-backend
docker run -d --name backend -p 3001:3001 blueprint-backend
docker run -it --rm alpine sh           # Interativo e remove ao sair
docker run -e NODE_ENV=production app   # Com variáveis de ambiente

# Executar em background
docker run -d --name nginx -p 80:80 nginx

# Executar com volumes
docker run -v $(pwd):/app -w /app node npm install
docker run -v backend_logs:/app/logs backend
```

### 🔧 Gerenciar Containers

```bash
# Parar containers
docker stop container_name
docker stop $(docker ps -q)             # Parar todos

# Remover containers
docker rm container_name
docker rm $(docker ps -aq)              # Remover todos
docker container prune                  # Remover containers parados

# Reiniciar containers
docker restart container_name

# Logs de containers
docker logs container_name
docker logs -f container_name            # Follow logs
docker logs --tail 50 container_name    # Últimas 50 linhas
docker logs --since="2h" container_name # Últimas 2 horas
```

### 🔍 Inspecionar e Debug

```bash
# Inspecionar containers/imagens
docker inspect container_name
docker inspect image_name

# Executar comandos em containers rodando
docker exec -it container_name bash
docker exec -it container_name sh
docker exec container_name ls -la /app

# Copiar arquivos
docker cp file.txt container_name:/app/
docker cp container_name:/app/logs ./logs

# Estatísticas de uso
docker stats
docker stats container_name
```

### 🧹 Limpeza e Manutenção

```bash
# Limpeza geral
docker system prune                     # Remove dados não utilizados
docker system prune -a                  # Remove tudo não utilizado
docker system prune --volumes           # Inclui volumes

# Limpeza específica
docker container prune                  # Containers parados
docker image prune                      # Imagens órfãs
docker volume prune                     # Volumes não utilizados
docker network prune                    # Networks não utilizadas

# Ver uso de espaço
docker system df
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
```

### 📦 Docker Compose - Comandos Essenciais

```bash
# Verificar versão
docker-compose --version

# Subir serviços
docker-compose up                       # Foreground
docker-compose up -d                    # Background (detached)
docker-compose up backend               # Serviço específico
docker-compose up --build               # Rebuild antes de subir
docker-compose up --force-recreate      # Recriar containers

# Parar serviços
docker-compose down                     # Para e remove containers
docker-compose down -v                 # Inclui volumes
docker-compose stop                    # Apenas para (não remove)
docker-compose stop backend            # Serviço específico
```

### 🔧 Docker Compose - Gestão de Serviços

```bash
# Logs
docker-compose logs                     # Todos os serviços
docker-compose logs -f                  # Follow logs
docker-compose logs backend             # Serviço específico
docker-compose logs --tail=50 backend   # Últimas 50 linhas

# Executar comandos
docker-compose exec backend bash        # Shell no container
docker-compose exec backend cargo build # Comando específico
docker-compose run --rm backend cargo test # Executar e remover

# Status e informações
docker-compose ps                       # Status dos serviços
docker-compose top                      # Processos rodando
docker-compose config                   # Validar configuração
docker-compose config --services        # Listar serviços
```

### 🏗️ Docker Compose - Build e Deploy

```bash
# Build
docker-compose build                    # Build todos os serviços
docker-compose build backend            # Build serviço específico
docker-compose build --no-cache         # Build sem cache
docker-compose build --pull             # Pull imagens base antes

# Restart e recreate
docker-compose restart                  # Restart todos
docker-compose restart backend          # Restart específico
docker-compose up -d --force-recreate   # Recriar containers

# Scale serviços
docker-compose up -d --scale backend=3  # 3 instâncias do backend
```

### 📁 Docker Compose - Arquivos Múltiplos

```bash
# Usar arquivos específicos
docker-compose -f docker-compose.yml up
docker-compose -f docker-compose-dev.yml up
docker-compose -f docker-compose-prod.yml up

# Combinar arquivos
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

# Especificar projeto
docker-compose -p blueprint-blog up
docker-compose -p blueprint-blog-dev up
```

### 🌐 Docker Compose - Redes e Volumes

```bash
# Gerenciar volumes
docker-compose down -v                  # Remove volumes
docker volume ls                        # Listar volumes
docker volume inspect volume_name       # Inspecionar volume
docker volume rm volume_name            # Remover volume

# Gerenciar redes
docker network ls                       # Listar redes
docker network inspect network_name     # Inspecionar rede
docker network rm network_name          # Remover rede
```

### 🔍 Docker Compose - Debug e Troubleshooting

```bash
# Validar configuração
docker-compose config                   # Mostrar configuração final
docker-compose config -q               # Validar silenciosamente

# Debug de serviços
docker-compose ps                       # Status dos containers
docker-compose logs --tail=100 -f      # Logs em tempo real
docker-compose exec backend env         # Ver variáveis de ambiente

# Verificar conectividade
docker-compose exec frontend ping backend
docker-compose exec backend curl http://frontend:3000
```

### 🚀 Comandos Específicos do Blueprint Blog

```bash
# Desenvolvimento
docker-compose -f docker-compose-dev.yml up -d
docker-compose -f docker-compose-dev.yml logs -f backend
docker-compose -f docker-compose-dev.yml exec backend cargo check

# Produção
docker-compose -f docker-compose-prod.yml up -d
docker-compose -f docker-compose-prod.yml logs -f
docker-compose -f docker-compose-prod.yml exec backend curl http://localhost:3001/health

# Build específico
docker build -t blueprint-backend:latest ./backend
docker build -t blueprint-frontend:latest ./frontend

# Logs específicos
docker-compose logs -f nginx backend frontend
docker-compose logs --since="1h" backend | grep ERROR
```

### 🔧 Comandos de Manutenção do Projeto

```bash
# Backup de volumes
docker run --rm -v blueprint_backend_logs:/data -v $(pwd):/backup alpine tar czf /backup/logs-backup.tar.gz /data

# Restore de volumes
docker run --rm -v blueprint_backend_logs:/data -v $(pwd):/backup alpine tar xzf /backup/logs-backup.tar.gz -C /

# Health checks
docker-compose exec backend curl -f http://localhost:3001/health
docker-compose exec nginx curl -f http://localhost/health

# Monitoramento
docker stats $(docker-compose ps -q)
docker-compose top
```

### 🐛 Troubleshooting Docker

```bash
# Verificar se Docker está rodando
docker version
systemctl status docker              # Linux
Get-Service docker                   # Windows

# Problemas de permissão (Linux)
sudo usermod -aG docker $USER
newgrp docker

# Problemas de espaço
docker system df                     # Ver uso de espaço
docker system prune -a               # Limpar tudo

# Problemas de rede
docker network ls
docker network inspect bridge

# Verificar logs do Docker daemon
journalctl -u docker.service         # Linux
Get-EventLog -LogName Application -Source Docker # Windows

# Reset completo (cuidado!)
docker system prune -a --volumes
docker builder prune -a
```

---

## 🌐 Nginx - Servidor Web & Proxy

### 📋 Comandos básicos Nginx

```bash
# Verificar versão e configuração
nginx -v
nginx -V                              # Versão com módulos compilados
nginx -t                              # Testar configuração
nginx -T                              # Testar e mostrar configuração

# Controlar serviço
sudo systemctl start nginx           # Iniciar
sudo systemctl stop nginx            # Parar
sudo systemctl restart nginx         # Reiniciar
sudo systemctl reload nginx          # Recarregar configuração
sudo systemctl status nginx          # Status do serviço
sudo systemctl enable nginx          # Habilitar no boot

# Comandos diretos
sudo nginx                           # Iniciar
sudo nginx -s reload                 # Recarregar
sudo nginx -s stop                   # Parar
sudo nginx -s quit                   # Parar gracefully
sudo nginx -s reopen                 # Reabrir logs
```

### 🔧 Configuração e Arquivos

```bash
# Localização dos arquivos (Ubuntu/Debian)
/etc/nginx/nginx.conf                # Configuração principal
/etc/nginx/sites-available/          # Sites disponíveis
/etc/nginx/sites-enabled/            # Sites habilitados
/etc/nginx/conf.d/                   # Configurações adicionais
/var/log/nginx/                      # Logs
/var/www/html/                       # Diretório web padrão

# Localização dos arquivos (CentOS/RHEL)
/etc/nginx/nginx.conf                # Configuração principal
/etc/nginx/conf.d/                   # Configurações de sites
/var/log/nginx/                      # Logs
/usr/share/nginx/html/               # Diretório web padrão

# Editar configurações
sudo nano /etc/nginx/nginx.conf
sudo nano /etc/nginx/sites-available/default
sudo nano /etc/nginx/sites-available/blueprint-blog

# Habilitar/desabilitar sites
sudo ln -s /etc/nginx/sites-available/blueprint-blog /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/blueprint-blog
sudo a2ensite blueprint-blog         # Habilitar site
sudo a2dissite blueprint-blog        # Desabilitar site
```

### 📊 Logs e Monitoramento

```bash
# Ver logs em tempo real
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log | grep "POST"

# Logs com filtros
sudo grep "404" /var/log/nginx/access.log
sudo grep "error" /var/log/nginx/error.log
sudo grep "$(date '+%d/%b/%Y')" /var/log/nginx/access.log

# Analisar logs
sudo awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr
sudo awk '{print $7}' /var/log/nginx/access.log | sort | uniq -c | sort -nr

# Rotação de logs
sudo logrotate -f /etc/logrotate.d/nginx
sudo nginx -s reopen                 # Reabrir logs após rotação
```

### 🔍 Debug e Troubleshooting

```bash
# Verificar sintaxe da configuração
sudo nginx -t
sudo nginx -T | grep -A 10 -B 10 "server_name"

# Verificar processos
ps aux | grep nginx
sudo netstat -tlnp | grep nginx
sudo ss -tlnp | grep nginx

# Verificar portas em uso
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443
sudo lsof -i :80
sudo lsof -i :443

# Verificar configuração ativa
sudo nginx -T | grep -E "(server_name|listen|root|proxy_pass)"

# Debug de SSL
openssl s_client -connect blueprintblog.tech:443 -servername blueprintblog.tech
sudo nginx -T | grep -A 5 -B 5 ssl
```

### 🚀 Configurações Específicas do Blueprint Blog

```bash
# Configuração para desenvolvimento
server {
    listen 80;
    server_name localhost;

    # Frontend (React/Next.js)
    location / {
        proxy_pass http://frontend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API (Rust)
    location /api/ {
        proxy_pass http://backend:3001/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Configuração para produção
server {
    listen 80;
    listen 443 ssl http2;
    server_name blueprintblog.tech www.blueprintblog.tech;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/blueprintblog.tech/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/blueprintblog.tech/privkey.pem;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

### 🔧 Comandos de Manutenção Nginx

```bash
# Backup de configuração
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
sudo tar -czf nginx-config-backup-$(date +%Y%m%d).tar.gz /etc/nginx/

# Restore de configuração
sudo cp /etc/nginx/nginx.conf.backup /etc/nginx/nginx.conf
sudo nginx -t && sudo systemctl reload nginx

# Verificar uso de recursos
sudo nginx -T | grep worker_processes
sudo nginx -T | grep worker_connections
ps aux | grep nginx | awk '{sum+=$6} END {print "Total Memory: " sum/1024 " MB"}'

# Limpar cache (se configurado)
sudo rm -rf /var/cache/nginx/*
sudo systemctl reload nginx
```

---

## 🐧 Linux - Sistema e Administração

### 📋 Comandos básicos do Sistema

```bash
# Informações do sistema
uname -a                             # Informações do kernel
lsb_release -a                       # Informações da distribuição
hostnamectl                          # Informações do host
uptime                               # Tempo de atividade
whoami                               # Usuário atual
id                                   # ID do usuário e grupos
w                                    # Usuários logados
last                                 # Últimos logins

# Informações de hardware
lscpu                                # Informações da CPU
free -h                              # Uso de memória
df -h                                # Uso de disco
lsblk                                # Dispositivos de bloco
lsusb                                # Dispositivos USB
lspci                                # Dispositivos PCI
```

### 📁 Navegação e Arquivos

```bash
# Navegação
pwd                                  # Diretório atual
ls -la                               # Listar arquivos detalhado
ls -lah                              # Com tamanhos legíveis
tree                                 # Estrutura em árvore
find /path -name "*.log"             # Buscar arquivos
locate filename                     # Localizar arquivos (updatedb)

# Manipulação de arquivos
cp file1 file2                       # Copiar arquivo
cp -r dir1 dir2                      # Copiar diretório
mv file1 file2                       # Mover/renomear
rm file                              # Remover arquivo
rm -rf directory                     # Remover diretório
mkdir -p path/to/dir                 # Criar diretórios
chmod 755 file                       # Alterar permissões
chown user:group file                # Alterar proprietário
```

### 🔍 Busca e Filtros

```bash
# Grep - busca em arquivos
grep "error" /var/log/nginx/error.log
grep -r "function" /path/to/code/
grep -i "warning" *.log              # Case insensitive
grep -v "debug" app.log               # Inverter busca
grep -n "TODO" *.js                  # Mostrar números de linha
grep -A 5 -B 5 "error" app.log       # Contexto (5 linhas antes/depois)

# Find - buscar arquivos
find . -name "*.js" -type f
find . -size +100M                   # Arquivos maiores que 100MB
find . -mtime -7                     # Modificados nos últimos 7 dias
find . -name "*.log" -exec rm {} \;  # Executar comando nos resultados

# Awk e Sed
awk '{print $1}' access.log          # Imprimir primeira coluna
sed 's/old/new/g' file.txt           # Substituir texto
sed -i 's/old/new/g' file.txt        # Substituir in-place
```

### 🔧 Processos e Serviços

```bash
# Processos
ps aux                               # Listar todos os processos
ps aux | grep nginx                  # Filtrar processos
top                                  # Monitor de processos em tempo real
htop                                 # Monitor melhorado (se instalado)
kill PID                             # Matar processo por PID
killall nginx                       # Matar todos os processos nginx
pgrep nginx                          # Encontrar PID por nome
pkill nginx                          # Matar processo por nome

# Serviços (systemd)
sudo systemctl status nginx         # Status do serviço
sudo systemctl start nginx          # Iniciar serviço
sudo systemctl stop nginx           # Parar serviço
sudo systemctl restart nginx        # Reiniciar serviço
sudo systemctl enable nginx         # Habilitar no boot
sudo systemctl disable nginx        # Desabilitar no boot
sudo systemctl list-units --type=service # Listar serviços
```

### 🌐 Rede e Conectividade

```bash
# Informações de rede
ip addr show                         # Interfaces de rede
ip route show                        # Tabela de roteamento
netstat -tlnp                        # Portas abertas
ss -tlnp                             # Alternativa moderna ao netstat
lsof -i :80                          # Processos usando porta 80

# Conectividade
ping google.com                      # Testar conectividade
curl -I http://blueprintblog.tech    # Testar HTTP
wget http://example.com/file         # Baixar arquivo
nslookup blueprintblog.tech          # Resolver DNS
dig blueprintblog.tech               # DNS lookup detalhado

# Firewall (UFW)
sudo ufw status                      # Status do firewall
sudo ufw enable                      # Habilitar firewall
sudo ufw allow 80                    # Permitir porta 80
sudo ufw allow 443                   # Permitir porta 443
sudo ufw deny 22                     # Negar porta 22
sudo ufw delete allow 80             # Remover regra
```

### 📊 Monitoramento e Performance

```bash
# CPU e Memória
top                                  # Monitor em tempo real
htop                                 # Monitor melhorado
vmstat 1                             # Estatísticas de VM
iostat 1                             # Estatísticas de I/O
sar -u 1 10                          # CPU usage (10 samples)

# Disco
df -h                                # Uso de disco
du -sh /path/to/dir                  # Tamanho de diretório
du -h --max-depth=1 /var/log         # Tamanho por subdiretório
iotop                                # Monitor de I/O por processo
lsof +D /path/to/dir                 # Arquivos abertos em diretório

# Rede
iftop                                # Monitor de tráfego de rede
nethogs                              # Uso de rede por processo
ss -s                                # Estatísticas de sockets
```

### 🔐 Usuários e Permissões

```bash
# Usuários
sudo adduser username               # Adicionar usuário
sudo deluser username               # Remover usuário
sudo usermod -aG sudo username      # Adicionar ao grupo sudo
su - username                       # Trocar de usuário
sudo -u username command            # Executar como outro usuário

# Grupos
groups                              # Grupos do usuário atual
sudo addgroup groupname             # Criar grupo
sudo usermod -aG groupname username # Adicionar usuário ao grupo

# Permissões
chmod 755 file                      # rwxr-xr-x
chmod u+x file                      # Adicionar execução para owner
chmod g-w file                      # Remover escrita para grupo
chown user:group file               # Alterar proprietário
chown -R user:group directory       # Recursivo
```

### 📦 Gerenciamento de Pacotes

```bash
# Ubuntu/Debian (APT)
sudo apt update                     # Atualizar lista de pacotes
sudo apt upgrade                    # Atualizar pacotes
sudo apt install package           # Instalar pacote
sudo apt remove package            # Remover pacote
sudo apt purge package             # Remover pacote e configurações
sudo apt autoremove                # Remover dependências órfãs
sudo apt search keyword            # Buscar pacotes
apt list --installed               # Listar pacotes instalados

# CentOS/RHEL (YUM/DNF)
sudo yum update                     # Atualizar pacotes
sudo yum install package           # Instalar pacote
sudo yum remove package            # Remover pacote
sudo yum search keyword            # Buscar pacotes
yum list installed                 # Listar pacotes instalados

# Snap packages
sudo snap install package          # Instalar snap
sudo snap remove package           # Remover snap
snap list                          # Listar snaps instalados
```

### 🔧 Comandos Específicos para Blueprint Blog

```bash
# Instalação de dependências do projeto
sudo apt update && sudo apt install -y curl wget git build-essential

# Instalar Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Instalar Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Instalar Node.js (via NodeSource)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instalar Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Instalar Nginx
sudo apt install nginx
sudo systemctl enable nginx
sudo systemctl start nginx

# Verificar instalações
docker --version
docker-compose --version
node --version
npm --version
cargo --version
nginx -v
```

### 🚀 Deploy e Produção

```bash
# Preparar servidor para Blueprint Blog
sudo mkdir -p /opt/blueprint-blog
sudo chown $USER:$USER /opt/blueprint-blog
cd /opt/blueprint-blog

# Clonar repositório
git clone https://github.com/user/blueprint-blog.git .

# Configurar variáveis de ambiente
cp .env.example .env
nano .env

# Build e deploy
docker-compose -f docker-compose-prod.yml build
docker-compose -f docker-compose-prod.yml up -d

# Verificar status
docker-compose -f docker-compose-prod.yml ps
docker-compose -f docker-compose-prod.yml logs -f

# Configurar SSL com Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d blueprintblog.tech -d www.blueprintblog.tech

# Renovação automática SSL
sudo crontab -e
# Adicionar: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 🔍 Troubleshooting Linux Específico

```bash
# Verificar logs do sistema
sudo journalctl -u nginx -f
sudo journalctl -u docker -f
sudo journalctl --since "1 hour ago"

# Verificar espaço em disco
df -h
du -sh /var/log/*
du -sh /var/lib/docker/*

# Limpar logs antigos
sudo journalctl --vacuum-time=7d
sudo find /var/log -name "*.log" -type f -mtime +30 -delete

# Verificar conectividade de rede
ping -c 4 8.8.8.8
curl -I https://blueprintblog.tech
telnet blueprintblog.tech 80
telnet blueprintblog.tech 443

# Verificar DNS
nslookup blueprintblog.tech
dig blueprintblog.tech A
dig blueprintblog.tech AAAA

# Verificar certificados SSL
openssl s_client -connect blueprintblog.tech:443 -servername blueprintblog.tech
curl -vI https://blueprintblog.tech

# Monitorar recursos em tempo real
watch -n 1 'free -h && echo && df -h && echo && docker stats --no-stream'
```

---

## 📁 Sistema & Arquivos

### PowerShell

```powershell
# Navegar e listar
Get-Location
Get-ChildItem -Path . -Force
Get-ChildItem -Path backend -Recurse

# Mover arquivos
Move-Item "src\file.jsx" "src\file.tsx"

# Remover arquivos/pastas
Remove-Item -Recurse -Force node_modules
Remove-Item file.txt

# Verificar conteúdo
Get-Content package.json | Select-String -A 5 -B 1 "scripts"
type package.json
```

### Bash/WSL

```bash
# Navegar e listar
pwd
ls -la
find . -name "*.tsx" -type f

# Mover arquivos
mv src/file.jsx src/file.tsx

# Remover arquivos/pastas
rm -rf node_modules
rm file.txt

# Verificar conteúdo
cat package.json
grep -A 5 -B 1 "scripts" package.json
```

---

## 🔧 Troubleshooting

### Problemas comuns

#### Frontend não carrega (404)

```bash
# Verificar se o servidor está rodando
npm run dev

# Limpar cache e reinstalar
npm cache clean --force
rm -rf node_modules
npm install

# Verificar porta
netstat -ano | findstr :5173  # Windows
lsof -i :5173                 # Linux/Mac
```

#### Erros de TypeScript

```bash
# Verificar erros
npx tsc --noEmit

# Limpar cache do TypeScript
rm -rf node_modules/.cache
```

#### Problemas com Rust

```bash
# Verificar instalação
cargo --version

# Limpar build
cargo clean

# Atualizar Rust
rustup update
```

#### Problemas de PATH

```powershell
# Windows - Adicionar ao PATH
$env:PATH += ";$env:USERPROFILE\.cargo\bin"

# Verificar PATH
echo $env:PATH
```

```bash
# Linux/WSL - Adicionar ao PATH
export PATH="$HOME/.cargo/bin:$PATH"
echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc

# Verificar PATH
echo $PATH
```

---

## 📊 Monitoramento

### Performance

```bash
# Verificar tamanho do bundle
npm run build
ls -lh dist/assets/

# Analisar dependências
npm ls
npm audit
```

### Logs

```bash
# Logs do backend (Rust)
RUST_LOG=debug cargo run

# Logs do frontend
npm run dev -- --debug
```

---

## 🎯 Comandos Específicos do Projeto

### Migração TypeScript

```bash
# Verificar arquivos JS/JSX restantes
Get-ChildItem -Path src -Recurse -Include *.js,*.jsx | Measure-Object

# Renomear arquivos
Move-Item "Component.jsx" "Component.tsx"
```

### Configuração do ambiente

```bash
# Copiar arquivo de exemplo
cp .env.example .env

# Verificar configuração
cat .env
```

### Comandos específicos do domínio blueprintblog.tech

```bash
# Testar conectividade do domínio
nslookup blueprintblog.tech
curl -I https://blueprintblog.tech/health

# Verificar certificado SSL
openssl s_client -connect blueprintblog.tech:443 -servername blueprintblog.tech

# Testar APIs
curl -X GET https://blueprintblog.tech/api/health
curl -X GET https://blueprintblog.tech/api/posts

# Deploy para produção
make deploy-prod

# Verificar logs de produção
docker-compose logs -f nginx
docker-compose logs -f backend
```

---

## 🥷 Comandos Ninja Avançados

### Git Avançado

```bash
# Buscar em todo o histórico
git log --all --grep="TypeScript"
git log --oneline --graph --all --decorate

# Encontrar quando um arquivo foi deletado
git log --full-history -- path/to/file

# Buscar código em commits
git log -S "função específica" --source --all

# Desfazer commits (cuidado!)
git reset --soft HEAD~1  # Mantém mudanças staged
git reset --hard HEAD~1  # APAGA TUDO!

# Stash avançado
git stash push -m "WIP: feature X" -- src/
git stash list
git stash apply stash@{0}

# Rebase interativo (reescrever história)
git rebase -i HEAD~3

# Cherry-pick commits específicos
git cherry-pick abc123def

# Bisect para encontrar bugs
git bisect start
git bisect bad HEAD
git bisect good v1.0.0
```

### Node.js Ninja

```bash
# Analisar dependências duplicadas
npm ls --depth=0
npm dedupe

# Verificar vulnerabilidades
npm audit --audit-level high
npm audit fix --force

# Executar scripts em paralelo
npm run dev & npm run backend

# Verificar qual processo usa uma porta
netstat -ano | findstr :3000
taskkill /PID 1234 /F

# NPM com diferentes registries
npm install --registry https://registry.npmjs.org/
npm config set registry https://registry.npmjs.org/

# Limpar tudo (nuclear option)
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# Verificar tamanho de pacotes
npm install -g bundlephobia-cli
bundlephobia react

# Executar scripts sem npm
npx --no-install vite build
```

### PowerShell Ninja

```powershell
# Buscar texto em arquivos
Select-String -Path "src\**\*.tsx" -Pattern "useState"
Get-ChildItem -Recurse -Include "*.ts","*.tsx" | Select-String "interface"

# Operações em massa
Get-ChildItem -Filter "*.jsx" -Recurse | ForEach-Object {
    Rename-Item $_.FullName ($_.Name -replace "\.jsx$", ".tsx")
}

# Monitorar mudanças em arquivos
Get-ChildItem -Path "src" -Recurse | Get-FileHash | Export-Csv baseline.csv

# Comparar diretórios
Compare-Object (Get-ChildItem src) (Get-ChildItem backup) -Property Name

# Executar comandos em paralelo
Start-Job -ScriptBlock { npm run build }
Get-Job | Receive-Job

# Variáveis de ambiente avançadas
[Environment]::SetEnvironmentVariable("NODE_ENV", "development", "User")
Get-ChildItem Env: | Where-Object Name -like "*NODE*"

# Aliases úteis
Set-Alias ll Get-ChildItem
Set-Alias grep Select-String

# Histórico de comandos
Get-History | Where-Object CommandLine -like "*npm*"
```

### Bash/WSL Ninja

```bash
# Find avançado
find . -name "*.tsx" -exec grep -l "useState" {} \;
find . -type f -name "*.js" -delete

# Sed para substituições em massa
find src -name "*.tsx" -exec sed -i 's/React.FC/FC/g' {} \;

# Awk para processamento de texto
npm ls --json | jq '.dependencies | keys[]'

# Xargs para operações em lote
find . -name "*.log" | xargs rm
ls *.jsx | xargs -I {} mv {} {}.backup

# Monitoramento em tempo real
watch -n 2 'npm run build'
tail -f logs/app.log | grep ERROR

# Aliases ninja
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'
alias grep='grep --color=auto'
alias fgrep='fgrep --color=auto'
alias egrep='egrep --color=auto'

# Funções úteis
function mkcd() { mkdir -p "$1" && cd "$1"; }
function backup() { cp "$1"{,.backup}; }
function extract() {
    case $1 in
        *.tar.bz2) tar xjf $1 ;;
        *.tar.gz) tar xzf $1 ;;
        *.zip) unzip $1 ;;
    esac
}

# Histórico inteligente
export HISTSIZE=10000
export HISTCONTROL=ignoredups:erasedups
```

### TypeScript Ninja

```bash
# Verificar tipos específicos
npx tsc --noEmit --listFiles | grep Component

# Gerar relatório de tipos
npx tsc --noEmit --pretty --listEmittedFiles

# Verificar compatibilidade de versões
npx tsc --showConfig | jq '.compilerOptions.target'

# Debug de resolução de módulos
npx tsc --traceResolution --noEmit src/main.tsx

# Verificar apenas arquivos modificados
git diff --name-only HEAD~1 | grep -E '\.(ts|tsx)$' | xargs npx tsc --noEmit

# Análise de dependências circulares
npx madge --circular --extensions ts,tsx src/

# Verificar imports não utilizados
npx ts-unused-exports tsconfig.json
```

### Docker Ninja

```bash
# Limpeza completa
docker system prune -a --volumes
docker builder prune

# Inspecionar imagens
docker history blueprint-backend
docker inspect blueprint-backend

# Logs avançados
docker logs -f --tail 100 container_name
docker logs --since="2h" container_name

# Executar comandos em containers
docker exec -it container_name bash
docker exec container_name ls -la /app

# Multi-stage builds
docker build --target development .
docker build --target production .

# Docker Compose avançado
docker-compose up --scale backend=3
docker-compose logs -f backend
docker-compose exec backend bash

# Backup de volumes
docker run --rm -v myvolume:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz /data
```

### Performance & Monitoring

```bash
# Monitorar uso de CPU/Memória
# Windows
Get-Process | Sort-Object CPU -Descending | Select-Object -First 10
Get-Counter "\Process(*)\% Processor Time"

# Linux
top -p $(pgrep node)
htop
iotop

# Monitorar rede
netstat -an | grep :3000
ss -tulpn | grep :3000

# Benchmark de build
time npm run build
Measure-Command { npm run build }

# Análise de bundle
npx webpack-bundle-analyzer dist/static/js/*.js
npx source-map-explorer dist/static/js/*.js

# Lighthouse CI
npm install -g @lhci/cli
lhci autorun
```

### Debugging Avançado

```bash
# Node.js debugging
node --inspect-brk=0.0.0.0:9229 node_modules/.bin/vite
node --inspect --inspect-port=9229 server.js

# Rust debugging
RUST_BACKTRACE=1 cargo run
RUST_BACKTRACE=full cargo run
RUST_LOG=debug,hyper=info cargo run

# Strace/ltrace (Linux)
strace -e trace=file cargo build
ltrace -e malloc cargo run

# Memory profiling
valgrind --tool=memcheck cargo run
```

### Automação & Scripts

```bash
# Git hooks
# .git/hooks/pre-commit
#!/bin/sh
npm run lint && npm run test

# Makefile para automação
.PHONY: dev build test clean
dev:
	npm run dev
build:
	npm run build
test:
	npm test && cargo test
clean:
	rm -rf node_modules target dist

# Scripts de deploy
#!/bin/bash
set -e
npm run build
docker build -t app:latest .
docker push registry.com/app:latest
kubectl apply -f k8s/
```

### Truques de Produtividade

```bash
# Aliases para projetos
alias blog='cd ~/projects/blueprint-blog'
alias be='cd ~/projects/blueprint-blog/backend'
alias fe='cd ~/projects/blueprint-blog/frontend'

# Funções para desenvolvimento
function dev() {
    cd ~/projects/blueprint-blog
    code .
    npm run dev
}

function deploy() {
    git add .
    git commit -m "${1:-Update}"
    git push
    npm run build
}

# Tmux/Screen para sessões persistentes
tmux new-session -d -s blog
tmux send-keys -t blog 'cd ~/projects/blueprint-blog && npm run dev' Enter

# Watchdog para restart automático
nodemon --watch src --ext ts,tsx --exec "npm run build"
cargo watch -x run
```

### Troubleshooting Ninja

```bash
# Verificar locks de arquivos
lsof +D /path/to/directory
fuser -v /path/to/file

# Verificar processos zumbis
ps aux | grep -E "(Z|<defunct>)"

# Limpar DNS cache
# Windows
ipconfig /flushdns
# Linux
sudo systemctl flush-dns

# Verificar conectividade
curl -I http://localhost:3000
telnet localhost 3000
nc -zv localhost 3000

# Verificar certificados SSL
openssl s_client -connect domain.com:443
curl -vI https://domain.com

# Análise de logs
tail -f /var/log/nginx/error.log | grep -E "(error|warning)"
journalctl -u nginx -f --since "1 hour ago"
```

---

_📝 Documento criado durante a migração TypeScript 100% do Blueprint Blog_
_🗓️ Data: Janeiro 2025_
_🥷 Comandos ninja adicionados para máxima produtividade!_
