# 🛠️ Comandos Ú<PERSON>is - Blueprint Blog

Este documento contém comandos úteis para desenvolvimento, manutenção e troubleshooting do projeto.

**🌐 Domínio:** https://blueprintblog.tech

## 📋 Índice

- [Git & Versionamento](#git--versionamento)
- [Node.js & Frontend](#nodejs--frontend)
- [Rust & Backend](#rust--backend)
- [TypeScript](#typescript)
- [Docker](#docker)
- [Sistema & Arquivos](#sistema--arquivos)
- [Troubleshooting](#troubleshooting)

---

## 🔄 Git & Versionamento

### Verificar status e histórico
```bash
# Status atual
git status

# Histórico de commits
git log --oneline -5

# Ver mudanças no último commit
git show --name-status HEAD

# Ver arquivos rastreados em um diretório
git ls-files backend/

# Restaurar arquivos de um commit
git checkout HEAD -- backend/
```

### Commits e branches
```bash
# Adicionar todos os arquivos
git add .

# Commit com mensagem
git commit -m "feat: Complete TypeScript migration to 100%"

# Ver diferenças
git diff
git diff --cached
```

---

## 📦 Node.js & Frontend

### Gerenciamento de dependências
```bash
# Instalar dependências
npm install

# Limpar cache
npm cache clean --force

# Verificar scripts disponíveis
npm run

# Remover node_modules e reinstalar
Remove-Item -Recurse -Force node_modules  # PowerShell
rm -rf node_modules                        # Bash
npm install
```

### Build e desenvolvimento
```bash
# Servidor de desenvolvimento
npm run dev

# Build de produção
npm run build

# Preview do build
npm run preview

# Verificar bundle size
npm run build -- --analyze
```

### Verificar estrutura de arquivos
```bash
# Contar arquivos por extensão
Get-ChildItem -Path src -Recurse -Include *.js,*.jsx | Measure-Object | Select-Object Count
Get-ChildItem -Path src -Recurse -Include *.ts,*.tsx | Measure-Object | Select-Object Count

# Listar arquivos específicos
Get-ChildItem -Path src -Recurse -Include *.js,*.jsx | Select-Object Name,Directory
```

---

## 🦀 Rust & Backend

### Instalação do Rust
```bash
# Windows (PowerShell)
Invoke-WebRequest -Uri "https://win.rustup.rs/x86_64" -OutFile "rustup-init.exe"
.\rustup-init.exe --default-toolchain stable --profile default --target x86_64-pc-windows-msvc -y

# Linux/WSL
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### Comandos Cargo
```bash
# Verificar versão
cargo --version
rustc --version

# Compilar projeto
cargo build

# Executar projeto
cargo run

# Executar com logs
RUST_LOG=debug cargo run

# Verificar código
cargo check

# Executar testes
cargo test

# Atualizar dependências
cargo update
```

---

## 🔍 TypeScript

### Verificação de tipos
```bash
# Verificar erros sem compilar
npx tsc --noEmit

# Verificar arquivo específico
npx tsc --noEmit src/components/Component.tsx

# Gerar arquivos de declaração
npx tsc --declaration --emitDeclarationOnly
```

### Configuração
```bash
# Verificar configuração
npx tsc --showConfig

# Inicializar tsconfig.json
npx tsc --init
```

---

## 🐳 Docker

### Comandos básicos
```bash
# Verificar versão
docker --version
docker-compose --version

# Listar containers
docker ps
docker ps -a

# Construir imagem
docker build -t blueprint-backend ./backend

# Executar container
docker run -p 3001:3001 blueprint-backend

# Docker Compose
docker-compose up
docker-compose up backend
docker-compose down
```

---

## 📁 Sistema & Arquivos

### PowerShell
```powershell
# Navegar e listar
Get-Location
Get-ChildItem -Path . -Force
Get-ChildItem -Path backend -Recurse

# Mover arquivos
Move-Item "src\file.jsx" "src\file.tsx"

# Remover arquivos/pastas
Remove-Item -Recurse -Force node_modules
Remove-Item file.txt

# Verificar conteúdo
Get-Content package.json | Select-String -A 5 -B 1 "scripts"
type package.json
```

### Bash/WSL
```bash
# Navegar e listar
pwd
ls -la
find . -name "*.tsx" -type f

# Mover arquivos
mv src/file.jsx src/file.tsx

# Remover arquivos/pastas
rm -rf node_modules
rm file.txt

# Verificar conteúdo
cat package.json
grep -A 5 -B 1 "scripts" package.json
```

---

## 🔧 Troubleshooting

### Problemas comuns

#### Frontend não carrega (404)
```bash
# Verificar se o servidor está rodando
npm run dev

# Limpar cache e reinstalar
npm cache clean --force
rm -rf node_modules
npm install

# Verificar porta
netstat -ano | findstr :5173  # Windows
lsof -i :5173                 # Linux/Mac
```

#### Erros de TypeScript
```bash
# Verificar erros
npx tsc --noEmit

# Limpar cache do TypeScript
rm -rf node_modules/.cache
```

#### Problemas com Rust
```bash
# Verificar instalação
cargo --version

# Limpar build
cargo clean

# Atualizar Rust
rustup update
```

#### Problemas de PATH
```powershell
# Windows - Adicionar ao PATH
$env:PATH += ";$env:USERPROFILE\.cargo\bin"

# Verificar PATH
echo $env:PATH
```

```bash
# Linux/WSL - Adicionar ao PATH
export PATH="$HOME/.cargo/bin:$PATH"
echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc

# Verificar PATH
echo $PATH
```

---

## 📊 Monitoramento

### Performance
```bash
# Verificar tamanho do bundle
npm run build
ls -lh dist/assets/

# Analisar dependências
npm ls
npm audit
```

### Logs
```bash
# Logs do backend (Rust)
RUST_LOG=debug cargo run

# Logs do frontend
npm run dev -- --debug
```

---

## 🎯 Comandos Específicos do Projeto

### Migração TypeScript
```bash
# Verificar arquivos JS/JSX restantes
Get-ChildItem -Path src -Recurse -Include *.js,*.jsx | Measure-Object

# Renomear arquivos
Move-Item "Component.jsx" "Component.tsx"
```

### Configuração do ambiente
```bash
# Copiar arquivo de exemplo
cp .env.example .env

# Verificar configuração
cat .env
```

### Comandos específicos do domínio blueprintblog.tech
```bash
# Testar conectividade do domínio
nslookup blueprintblog.tech
curl -I https://blueprintblog.tech/health

# Verificar certificado SSL
openssl s_client -connect blueprintblog.tech:443 -servername blueprintblog.tech

# Testar APIs
curl -X GET https://blueprintblog.tech/api/health
curl -X GET https://blueprintblog.tech/api/posts

# Deploy para produção
make deploy-prod

# Verificar logs de produção
docker-compose logs -f nginx
docker-compose logs -f backend
```

---

## 🥷 Comandos Ninja Avançados

### Git Avançado
```bash
# Buscar em todo o histórico
git log --all --grep="TypeScript"
git log --oneline --graph --all --decorate

# Encontrar quando um arquivo foi deletado
git log --full-history -- path/to/file

# Buscar código em commits
git log -S "função específica" --source --all

# Desfazer commits (cuidado!)
git reset --soft HEAD~1  # Mantém mudanças staged
git reset --hard HEAD~1  # APAGA TUDO!

# Stash avançado
git stash push -m "WIP: feature X" -- src/
git stash list
git stash apply stash@{0}

# Rebase interativo (reescrever história)
git rebase -i HEAD~3

# Cherry-pick commits específicos
git cherry-pick abc123def

# Bisect para encontrar bugs
git bisect start
git bisect bad HEAD
git bisect good v1.0.0
```

### Node.js Ninja
```bash
# Analisar dependências duplicadas
npm ls --depth=0
npm dedupe

# Verificar vulnerabilidades
npm audit --audit-level high
npm audit fix --force

# Executar scripts em paralelo
npm run dev & npm run backend

# Verificar qual processo usa uma porta
netstat -ano | findstr :3000
taskkill /PID 1234 /F

# NPM com diferentes registries
npm install --registry https://registry.npmjs.org/
npm config set registry https://registry.npmjs.org/

# Limpar tudo (nuclear option)
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# Verificar tamanho de pacotes
npm install -g bundlephobia-cli
bundlephobia react

# Executar scripts sem npm
npx --no-install vite build
```

### PowerShell Ninja
```powershell
# Buscar texto em arquivos
Select-String -Path "src\**\*.tsx" -Pattern "useState"
Get-ChildItem -Recurse -Include "*.ts","*.tsx" | Select-String "interface"

# Operações em massa
Get-ChildItem -Filter "*.jsx" -Recurse | ForEach-Object {
    Rename-Item $_.FullName ($_.Name -replace "\.jsx$", ".tsx")
}

# Monitorar mudanças em arquivos
Get-ChildItem -Path "src" -Recurse | Get-FileHash | Export-Csv baseline.csv

# Comparar diretórios
Compare-Object (Get-ChildItem src) (Get-ChildItem backup) -Property Name

# Executar comandos em paralelo
Start-Job -ScriptBlock { npm run build }
Get-Job | Receive-Job

# Variáveis de ambiente avançadas
[Environment]::SetEnvironmentVariable("NODE_ENV", "development", "User")
Get-ChildItem Env: | Where-Object Name -like "*NODE*"

# Aliases úteis
Set-Alias ll Get-ChildItem
Set-Alias grep Select-String

# Histórico de comandos
Get-History | Where-Object CommandLine -like "*npm*"
```

### Bash/WSL Ninja
```bash
# Find avançado
find . -name "*.tsx" -exec grep -l "useState" {} \;
find . -type f -name "*.js" -delete

# Sed para substituições em massa
find src -name "*.tsx" -exec sed -i 's/React.FC/FC/g' {} \;

# Awk para processamento de texto
npm ls --json | jq '.dependencies | keys[]'

# Xargs para operações em lote
find . -name "*.log" | xargs rm
ls *.jsx | xargs -I {} mv {} {}.backup

# Monitoramento em tempo real
watch -n 2 'npm run build'
tail -f logs/app.log | grep ERROR

# Aliases ninja
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'
alias grep='grep --color=auto'
alias fgrep='fgrep --color=auto'
alias egrep='egrep --color=auto'

# Funções úteis
function mkcd() { mkdir -p "$1" && cd "$1"; }
function backup() { cp "$1"{,.backup}; }
function extract() {
    case $1 in
        *.tar.bz2) tar xjf $1 ;;
        *.tar.gz) tar xzf $1 ;;
        *.zip) unzip $1 ;;
    esac
}

# Histórico inteligente
export HISTSIZE=10000
export HISTCONTROL=ignoredups:erasedups
```

### TypeScript Ninja
```bash
# Verificar tipos específicos
npx tsc --noEmit --listFiles | grep Component

# Gerar relatório de tipos
npx tsc --noEmit --pretty --listEmittedFiles

# Verificar compatibilidade de versões
npx tsc --showConfig | jq '.compilerOptions.target'

# Debug de resolução de módulos
npx tsc --traceResolution --noEmit src/main.tsx

# Verificar apenas arquivos modificados
git diff --name-only HEAD~1 | grep -E '\.(ts|tsx)$' | xargs npx tsc --noEmit

# Análise de dependências circulares
npx madge --circular --extensions ts,tsx src/

# Verificar imports não utilizados
npx ts-unused-exports tsconfig.json
```

### Docker Ninja
```bash
# Limpeza completa
docker system prune -a --volumes
docker builder prune

# Inspecionar imagens
docker history blueprint-backend
docker inspect blueprint-backend

# Logs avançados
docker logs -f --tail 100 container_name
docker logs --since="2h" container_name

# Executar comandos em containers
docker exec -it container_name bash
docker exec container_name ls -la /app

# Multi-stage builds
docker build --target development .
docker build --target production .

# Docker Compose avançado
docker-compose up --scale backend=3
docker-compose logs -f backend
docker-compose exec backend bash

# Backup de volumes
docker run --rm -v myvolume:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz /data
```

### Performance & Monitoring
```bash
# Monitorar uso de CPU/Memória
# Windows
Get-Process | Sort-Object CPU -Descending | Select-Object -First 10
Get-Counter "\Process(*)\% Processor Time"

# Linux
top -p $(pgrep node)
htop
iotop

# Monitorar rede
netstat -an | grep :3000
ss -tulpn | grep :3000

# Benchmark de build
time npm run build
Measure-Command { npm run build }

# Análise de bundle
npx webpack-bundle-analyzer dist/static/js/*.js
npx source-map-explorer dist/static/js/*.js

# Lighthouse CI
npm install -g @lhci/cli
lhci autorun
```

### Debugging Avançado
```bash
# Node.js debugging
node --inspect-brk=0.0.0.0:9229 node_modules/.bin/vite
node --inspect --inspect-port=9229 server.js

# Rust debugging
RUST_BACKTRACE=1 cargo run
RUST_BACKTRACE=full cargo run
RUST_LOG=debug,hyper=info cargo run

# Strace/ltrace (Linux)
strace -e trace=file cargo build
ltrace -e malloc cargo run

# Memory profiling
valgrind --tool=memcheck cargo run
```

### Automação & Scripts
```bash
# Git hooks
# .git/hooks/pre-commit
#!/bin/sh
npm run lint && npm run test

# Makefile para automação
.PHONY: dev build test clean
dev:
	npm run dev
build:
	npm run build
test:
	npm test && cargo test
clean:
	rm -rf node_modules target dist

# Scripts de deploy
#!/bin/bash
set -e
npm run build
docker build -t app:latest .
docker push registry.com/app:latest
kubectl apply -f k8s/
```

### Truques de Produtividade
```bash
# Aliases para projetos
alias blog='cd ~/projects/blueprint-blog'
alias be='cd ~/projects/blueprint-blog/backend'
alias fe='cd ~/projects/blueprint-blog/frontend'

# Funções para desenvolvimento
function dev() {
    cd ~/projects/blueprint-blog
    code .
    npm run dev
}

function deploy() {
    git add .
    git commit -m "${1:-Update}"
    git push
    npm run build
}

# Tmux/Screen para sessões persistentes
tmux new-session -d -s blog
tmux send-keys -t blog 'cd ~/projects/blueprint-blog && npm run dev' Enter

# Watchdog para restart automático
nodemon --watch src --ext ts,tsx --exec "npm run build"
cargo watch -x run
```

### Troubleshooting Ninja
```bash
# Verificar locks de arquivos
lsof +D /path/to/directory
fuser -v /path/to/file

# Verificar processos zumbis
ps aux | grep -E "(Z|<defunct>)"

# Limpar DNS cache
# Windows
ipconfig /flushdns
# Linux
sudo systemctl flush-dns

# Verificar conectividade
curl -I http://localhost:3000
telnet localhost 3000
nc -zv localhost 3000

# Verificar certificados SSL
openssl s_client -connect domain.com:443
curl -vI https://domain.com

# Análise de logs
tail -f /var/log/nginx/error.log | grep -E "(error|warning)"
journalctl -u nginx -f --since "1 hour ago"
```

---

*📝 Documento criado durante a migração TypeScript 100% do Blueprint Blog*
*🗓️ Data: Janeiro 2025*
*🥷 Comandos ninja adicionados para máxima produtividade!*
