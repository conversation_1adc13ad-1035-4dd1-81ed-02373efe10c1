# 🛡️ SVG Security - Blueprint Blog

## 🚨 Riscos de Segurança em SVG

### ⚠️ **Por que SVG pode ser perigoso?**

SVG (Scalable Vector Graphics) pode conter JavaScript e outros elementos maliciosos que representam riscos de segurança:

```xml
<!-- ❌ PERIGOSO: JavaScript em SVG -->
<svg onload="alert('XSS Attack!')">
  <script>
    // Código malicioso aqui
    window.location = 'http://malicious-site.com';
    localStorage.clear();
  </script>
</svg>

<!-- ❌ PERIGOSO: Event handlers -->
<svg>
  <circle onclick="stealUserData()" />
  <rect onmouseover="trackUser()" />
</svg>

<!-- ❌ PERIGOSO: External resources -->
<svg>
  <image href="http://tracker.com/pixel.gif" />
  <use href="http://malicious.com/exploit.svg#icon" />
</svg>
```

## 🛡️ Nossa Implementação de Segurança

### **1. DOMPurify - Sanitização Robusta**

Utilizamos o DOMPurify com configuração específica para SVG:

```javascript
const config = {
  USE_PROFILES: { svg: true, svgFilters: true },
  ALLOWED_TAGS: [
    'svg', 'g', 'path', 'circle', 'ellipse', 'line', 'rect', 
    'polyline', 'polygon', 'text', 'tspan', 'defs', 'clipPath', 
    'mask', 'pattern', 'linearGradient', 'radialGradient', 
    'stop', 'use', 'symbol', 'marker'
  ],
  FORBID_TAGS: ['script', 'object', 'embed', 'link', 'style', 'meta'],
  FORBID_ATTR: [
    'onload', 'onerror', 'onclick', 'onmouseover', 'href', 
    'xlink:href', 'src', 'data'
  ],
  ALLOW_DATA_ATTR: false,
  ALLOW_UNKNOWN_PROTOCOLS: false
}
```

### **2. Validações Adicionais**

- **✅ Parsing validation**: Verifica se o SVG é bem formado
- **✅ Size limits**: Limita dimensões máximas (1000x1000px)
- **✅ Namespace validation**: Força xmlns correto
- **✅ Content filtering**: Remove elementos perigosos

### **3. Indicadores Visuais**

- **🛡️ Shield verde**: SVG sanitizado e seguro
- **⚠️ Triângulo amarelo**: Modificações aplicadas
- **❌ Triângulo vermelho**: SVG bloqueado

## ✅ SVGs Seguros - Exemplos

### **Formas Básicas:**
```svg
<svg width="200" height="200" viewBox="0 0 200 200">
  <circle cx="100" cy="100" r="80" fill="#3B82F6" />
  <rect x="50" y="50" width="100" height="100" fill="#10B981" />
</svg>
```

### **Gradientes:**
```svg
<svg width="200" height="100" viewBox="0 0 200 100">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6" />
      <stop offset="100%" style="stop-color:#10B981" />
    </linearGradient>
  </defs>
  <rect width="200" height="100" fill="url(#grad1)" />
</svg>
```

### **Paths Complexos:**
```svg
<svg width="200" height="200" viewBox="0 0 200 200">
  <path d="M100,10 L150,90 L50,90 Z" fill="#F59E0B" />
  <path d="M100,190 Q150,140 100,90 Q50,140 100,190" fill="#EF4444" />
</svg>
```

## ❌ SVGs Bloqueados - Exemplos

### **JavaScript:**
```svg
<!-- ❌ SERÁ REMOVIDO -->
<svg onload="maliciousCode()">
  <script>alert('hack')</script>
</svg>
```

### **Event Handlers:**
```svg
<!-- ❌ ATRIBUTOS REMOVIDOS -->
<svg>
  <circle onclick="steal()" onmouseover="track()" />
</svg>
```

### **External Resources:**
```svg
<!-- ❌ HREF REMOVIDO -->
<svg>
  <image href="http://malicious.com/tracker.gif" />
</svg>
```

## 🔧 Configuração de Segurança

### **Tags Permitidas:**
- Elementos gráficos: `svg`, `g`, `path`, `circle`, `ellipse`, `line`, `rect`
- Texto: `text`, `tspan`
- Definições: `defs`, `clipPath`, `mask`, `pattern`
- Gradientes: `linearGradient`, `radialGradient`, `stop`
- Reutilização: `use`, `symbol`, `marker`

### **Atributos Permitidos:**
- Dimensões: `width`, `height`, `viewBox`
- Posicionamento: `x`, `y`, `cx`, `cy`, `r`, `rx`, `ry`
- Estilo: `fill`, `stroke`, `opacity`, `transform`
- Identificação: `class`, `id`

### **Elementos Proibidos:**
- Scripts: `script`, `object`, `embed`
- Estilos: `style`, `link`, `meta`
- Todos os event handlers (`on*`)
- Links externos (`href`, `xlink:href`, `src`)

## 🚀 Performance e Limites

### **Limites Aplicados:**
- **Largura máxima**: 1000px
- **Altura máxima**: 1000px
- **Timeout de processamento**: 5 segundos
- **Tamanho máximo do código**: 50KB

### **Otimizações:**
- Cache de SVGs sanitizados
- Lazy loading de componentes
- Debounce em edição em tempo real

## 📊 Monitoramento

### **Métricas Coletadas:**
- SVGs processados com sucesso
- SVGs bloqueados por segurança
- Tipos de modificações aplicadas
- Performance de sanitização

### **Logs de Segurança:**
```javascript
// Exemplo de log
{
  timestamp: "2024-01-15T10:30:00Z",
  action: "svg_sanitized",
  warnings: ["Dimensões limitadas", "Event handlers removidos"],
  originalSize: "2.5KB",
  sanitizedSize: "1.8KB"
}
```

## 🎯 Melhores Práticas

### **Para Usuários:**
1. **Use SVGs simples** para melhor performance
2. **Evite JavaScript** em SVGs (será removido)
3. **Teste sempre** no preview antes de publicar
4. **Mantenha dimensões razoáveis** (< 1000px)

### **Para Desenvolvedores:**
1. **Sempre sanitize** SVG de usuários
2. **Monitore logs** de segurança
3. **Atualize DOMPurify** regularmente
4. **Teste com SVGs maliciosos** em desenvolvimento

---

**🛡️ Segurança é prioridade no Blueprint Blog!**
