# 🌐 Configuração de Domínio - blueprintblog.duckdns.org

Este guia explica como configurar o domínio customizado `blueprintblog.duckdns.org` para o Blueprint Blog na AWS.

## 🎯 Visão Geral

O projeto está configurado para usar:
- **Domínio**: `blueprintblog.duckdns.org`
- **SSL/HTTPS**: Certificado automático via AWS Certificate Manager
- **DNS**: DuckDNS (gratuito) + Route 53 (opcional)
- **CDN**: Application Load Balancer com redirecionamento HTTPS

## 🚀 Opções de Configuração

### Opção 1: DuckDNS Simples (Recomendado)
Configuração mais simples usando apenas DuckDNS.

### Opção 2: Route 53 + DuckDNS
Configuração avançada com Route 53 para melhor controle DNS.

## 📋 Pré-requisitos

1. **Conta DuckDNS**: Registre-se em [duckdns.org](https://www.duckdns.org/)
2. **Token DuckDNS**: Obtenha seu token na dashboard
3. **Instância EC2**: Com Docker instalado
4. **Projeto deployado**: Execute `make ec2-deploy` primeiro

## 🛠️ Configuração Passo a Passo

### Passo 1: Configurar Token DuckDNS

```bash
# Exportar token DuckDNS
export DUCKDNS_TOKEN="seu_token_aqui"

# Ou adicionar ao ~/.bashrc para persistir
echo 'export DUCKDNS_TOKEN="seu_token_aqui"' >> ~/.bashrc
source ~/.bashrc
```

### Passo 2: Deploy na EC2

```bash
# Deploy completo na EC2
make ec2-deploy

# Ou manualmente
./scripts/deploy-ec2.sh
```

### Passo 3: Configurar DuckDNS

```bash
# Executar script de configuração DuckDNS
chmod +x scripts/setup-duckdns.sh
./scripts/setup-duckdns.sh
```

### Passo 4: Verificar Configuração

```bash
# Testar resolução DNS
nslookup blueprintblog.duckdns.org

# Testar conectividade
curl -I http://blueprintblog.duckdns.org/health
curl -I https://blueprintblog.duckdns.org/health
```

## 🔧 Configurações Avançadas

### SSL/TLS Certificate

O CloudFormation está configurado para criar automaticamente um certificado SSL:

```yaml
SSLCertificate:
  Type: AWS::CertificateManager::Certificate
  Properties:
    DomainName: blueprintblog.duckdns.org
    ValidationMethod: DNS
```

### Redirecionamento HTTP → HTTPS

O ALB está configurado para redirecionar automaticamente:

```yaml
ALBListenerHTTP:
  Type: AWS::ElasticLoadBalancingV2::Listener
  Properties:
    DefaultActions:
      - Type: redirect
        RedirectConfig:
          Protocol: HTTPS
          Port: 443
          StatusCode: HTTP_301
```

### Route 53 (Opcional)

Para usar Route 53 em vez de apenas DuckDNS:

1. **Criar Hosted Zone**:
```bash
aws route53 create-hosted-zone \
    --name blueprintblog.duckdns.org \
    --caller-reference $(date +%s)
```

2. **Configurar Name Servers** no DuckDNS

## 🔍 Troubleshooting

### Problema: DNS não resolve

```bash
# Verificar configuração DuckDNS
curl "https://www.duckdns.org/update?domains=blueprintblog&token=${DUCKDNS_TOKEN}&verbose=true"

# Testar diferentes DNS servers
nslookup blueprintblog.duckdns.org *******
nslookup blueprintblog.duckdns.org *******
```

### Problema: SSL não funciona

```bash
# Verificar status do certificado
aws acm list-certificates --region us-east-1

# Verificar validação DNS
aws acm describe-certificate --certificate-arn <cert-arn> --region us-east-1
```

### Problema: Site não carrega

```bash
# Verificar ALB
aws elbv2 describe-load-balancers --region us-east-1

# Verificar target group
aws elbv2 describe-target-health --target-group-arn <tg-arn> --region us-east-1

# Verificar logs ECS
aws logs tail /ecs/blueprint-blog --follow --region us-east-1
```

## 🔄 Renovação Automática DuckDNS

### Crontab Local
```bash
# Adicionar ao crontab para renovar a cada 5 minutos
crontab -e

# Adicionar linha:
*/5 * * * * curl -s "https://www.duckdns.org/update?domains=blueprintblog&token=${DUCKDNS_TOKEN}" > /dev/null
```

### Lambda Function (AWS)
```python
import urllib.request

def lambda_handler(event, context):
    token = "seu_token_aqui"
    domain = "blueprintblog"

    url = f"https://www.duckdns.org/update?domains={domain}&token={token}"
    response = urllib.request.urlopen(url)

    return {
        'statusCode': 200,
        'body': response.read().decode()
    }
```

## 📊 Monitoramento

### CloudWatch Alarms
```bash
# Criar alarm para health check
aws cloudwatch put-metric-alarm \
    --alarm-name "blueprint-blog-health" \
    --alarm-description "Blueprint Blog Health Check" \
    --metric-name HealthyHostCount \
    --namespace AWS/ApplicationELB \
    --statistic Average \
    --period 300 \
    --threshold 1 \
    --comparison-operator LessThanThreshold
```

### Route 53 Health Checks
```bash
# Criar health check
aws route53 create-health-check \
    --caller-reference $(date +%s) \
    --health-check-config Type=HTTPS,ResourcePath=/health,FullyQualifiedDomainName=blueprintblog.duckdns.org
```

## 💰 Custos

### DuckDNS
- **Gratuito** para uso pessoal

### AWS Route 53 (se usado)
- **Hosted Zone**: $0.50/mês
- **Queries**: $0.40 por milhão

### AWS Certificate Manager
- **Certificados SSL**: Gratuitos

## 🔐 Segurança

### Headers de Segurança
O Nginx está configurado com headers de segurança:

```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
```

### HTTPS Enforcement
- Redirecionamento automático HTTP → HTTPS
- HSTS headers (pode ser adicionado)
- Certificado SSL válido

## 📝 Comandos Úteis

```bash
# Verificar status completo
make status-aws

# Ver logs em tempo real
make logs-aws

# Testar domínio
curl -I https://blueprintblog.duckdns.org

# Verificar certificado SSL
openssl s_client -connect blueprintblog.duckdns.org:443 -servername blueprintblog.duckdns.org

# Atualizar DuckDNS manualmente
curl "https://www.duckdns.org/update?domains=blueprintblog&token=${DUCKDNS_TOKEN}&ip=$(curl -s ifconfig.me)"
```

## 🎉 Resultado Final

Após a configuração completa, você terá:

- ✅ **https://blueprintblog.duckdns.org** funcionando
- ✅ **Certificado SSL válido** e renovação automática
- ✅ **Redirecionamento HTTP → HTTPS**
- ✅ **Headers de segurança** configurados
- ✅ **Health checks** funcionando
- ✅ **Logs centralizados** no CloudWatch

## 🆘 Suporte

Se encontrar problemas:

1. **Verifique os logs**: `make logs-aws`
2. **Teste conectividade**: `curl -I https://blueprintblog.duckdns.org/health`
3. **Verifique DNS**: `nslookup blueprintblog.duckdns.org`
4. **Consulte este guia** para troubleshooting
5. **Abra uma issue** no repositório

---

**🌐 blueprintblog.duckdns.org está pronto para o mundo!**
