# 🐳 Blueprint Blog - Docker & AWS Deploy

Este documento contém todas as informações necessárias para containerizar e fazer deploy do Blueprint Blog na AWS.

## 📋 Pré-requisitos

### Ferramentas Necessárias

**Na instância EC2:**
- [Docker](https://docs.docker.com/get-docker/) (v20.10+)
- [Docker Compose](https://docs.docker.com/compose/install/) (v2.0+)
- [Git](https://git-scm.com/) (para clonar repositório)

**Para desenvolvimento local (opcional):**
- [Node.js](https://nodejs.org/) (v18+)
- [Make](https://www.gnu.org/software/make/) (para comandos simplificados)

### Configuração EC2

```bash
# Instalar Docker
sudo apt update
sudo apt install docker.io docker-compose -y
sudo usermod -aG docker $USER

# Reiniciar sessão para aplicar grupo docker
exit
# Fazer login novamente na EC2
```

## 🚀 Quick Start

### Desenvolvimento Local

```bash
# Usando Make (recomendado)
make dev

# Ou usando Docker Compose diretamente
docker-compose -f docker-compose.dev.yml up --build
```

### Produção Local

```bash
# Usando Make
make prod

# Ou usando Docker Compose
docker-compose up --build
```

### Deploy na EC2

```bash
# 1. Clonar repositório na EC2
git clone https://github.com/Genildocs/blueprint-blog.git
cd blueprint-blog

# 2. Configurar token DuckDNS
export DUCKDNS_TOKEN="seu_token_aqui"

# 3. Deploy completo (build acontece no Docker)
make ec2-deploy
```

**⚠️ Importante**: O build da aplicação React acontece **dentro do container Docker**. Não é necessário instalar Node.js na instância EC2!

## 📁 Estrutura dos Arquivos Docker

```
├── Dockerfile              # Imagem de produção
├── Dockerfile.dev          # Imagem de desenvolvimento
├── docker-compose.yml      # Produção local
├── docker-compose.dev.yml  # Desenvolvimento local
├── nginx.conf              # Configuração Nginx produção
├── supervisord.conf        # Configuração Supervisor
├── ecosystem.config.js     # Configuração PM2
├── .dockerignore           # Arquivos ignorados no build
├── Makefile                # Comandos simplificados
└── scripts/
    ├── deploy-ec2.sh       # Deploy direto na EC2
    └── setup-duckdns.sh    # Configuração DuckDNS
```

## 🔧 Comandos Disponíveis

### Desenvolvimento

```bash
make dev              # Iniciar desenvolvimento
make dev-detached     # Desenvolvimento em background
make dev-stop         # Parar desenvolvimento
make shell            # Abrir shell no container
```

### Build e Produção

```bash
make build            # Build da imagem Docker
make prod             # Produção local
make prod-detached    # Produção local em background
```

### Deploy

```bash
make ec2-deploy       # Deploy direto na EC2
make deploy-full      # Clean + Build + Deploy completo
```

### Domínio e DNS

```bash
make setup-domain     # Configurar DuckDNS
make test-domain      # Testar conectividade do domínio
```

### Monitoramento

```bash
make logs             # Logs locais
make status           # Status containers locais
```

### Limpeza

```bash
make clean            # Limpar containers locais
```

### Utilitários

```bash
make help             # Mostrar todos os comandos
make info             # Informações do projeto
make setup            # Setup inicial
```

## 🏗️ Arquitetura EC2

### Componentes

- **EC2 Instance** com Docker
- **Nginx** como proxy reverso
- **PM2** para gerenciamento de processos
- **Supervisor** para orquestração
- **DuckDNS** para DNS dinâmico

### Fluxo de Deploy

1. **Clone** do repositório na EC2
2. **Build** da aplicação React (dentro do Docker)
3. **Containerização** com Docker multi-stage
4. **Deploy** direto na instância EC2
5. **Configuração** automática do DuckDNS
6. **Verificação** de saúde da aplicação

## 🔒 Segurança

### Boas Práticas Implementadas

- ✅ **Usuário não-root** nos containers
- ✅ **Multi-stage build** para imagens menores
- ✅ **Security headers** no Nginx
- ✅ **Health checks** configurados
- ✅ **Logs centralizados** no CloudWatch
- ✅ **Network isolation** com Security Groups
- ✅ **HTTPS ready** (certificado SSL pode ser adicionado)

### Configurações de Segurança

```nginx
# Headers de segurança no Nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
```

## 📊 Monitoramento

### Health Checks

- **Container**: `curl -f http://localhost/health`
- **Nginx**: Endpoint `/health` com proxy para aplicação
- **PM2**: Health check integrado com restart automático

### Logs

```bash
# Logs locais
docker-compose logs -f

# Ou usando Make
make logs
```

### Métricas

- **Docker** coleta métricas dos containers
- **PM2** monitora CPU, memória e processos
- **Supervisor** gerencia logs centralizados

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. Erro de Build Docker

```bash
# Limpar cache do Docker
docker system prune -a

# Rebuild sem cache
docker build --no-cache -t blueprint-blog .
```

#### 2. Container não inicia

```bash
# Verificar logs
docker-compose logs blueprint-blog

# Verificar status
docker-compose ps
```

#### 3. Aplicação não responde

```bash
# Verificar health check
curl -f http://localhost/health

# Verificar portas
netstat -tlnp | grep :80
```

#### 4. DuckDNS não resolve

```bash
# Verificar configuração
nslookup blueprintblog.duckdns.org

# Testar conectividade
curl -I http://blueprintblog.duckdns.org/health
```

## 💰 Custos EC2

### Estimativa Mensal

- **EC2 t3.micro** (1 vCPU, 1 GB): ~$8-10
- **DuckDNS**: Gratuito
- **Elastic IP** (opcional): ~$3.65
- **Data Transfer**: ~$1-5

**Total estimado**: $10-20/mês

### Otimizações de Custo

- Use **t3.micro** para cargas leves
- **DuckDNS** é gratuito vs Route 53
- **Spot Instances** para reduzir custos
- **Reserved Instances** para uso contínuo

## 🔄 CI/CD

### GitHub Actions (exemplo)

```yaml
name: Deploy to EC2
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to EC2
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} '
            cd /path/to/blueprint-blog &&
            git pull origin main &&
            make ec2-deploy
          '
        env:
          DUCKDNS_TOKEN: ${{ secrets.DUCKDNS_TOKEN }}
```

## 📚 Recursos Adicionais

- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [EC2 Documentation](https://docs.aws.amazon.com/ec2/)
- [Nginx Configuration](https://nginx.org/en/docs/)
- [PM2 Documentation](https://pm2.keymetrics.io/docs/)
- [DuckDNS Documentation](https://www.duckdns.org/spec.jsp)

## 🆘 Suporte

Para problemas ou dúvidas:

1. Verifique os logs: `make logs`
2. Consulte este documento
3. Teste conectividade: `make test-domain`
4. Abra uma issue no repositório

---

**🚀 Blueprint Blog está pronto para produção na EC2!**
