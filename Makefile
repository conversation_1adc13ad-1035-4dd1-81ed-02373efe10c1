# Blueprint Blog - Makefile Final v2 🚀
.PHONY: help dev dev-build dev-fresh dev-restart dev-status dev-logs backend frontend build-dev build-prod prod-local prod-health deploy-check deploy-aws deploy-verify deploy-rollback deploy-full monitor perf-test security-scan backup-prod clean-all test-all env-check shell-backend shell-frontend lint format info setup

# Configurações
PROJECT_NAME := blueprint-blog
DOCKER_COMPOSE ?= docker-compose
COMPOSE_DEV_FILE := docker-compose-dev.yml
COMPOSE_PROD_FILE := docker-compose-prod.yml

# Cores para output
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

help: ## Mostrar ajuda
	@echo "$(GREEN)Blueprint Blog - Comandos Disponíveis$(NC)"
	@echo "======================================"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

### 🔥 Desenvolvimento

dev: ## Iniciar ambiente de desenvolvimento
	@echo "$(GREEN)🚀 Iniciando ambiente de desenvolvimento...$(NC)"
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) up -d
	@echo "$(GREEN)✅ Acesse: http://localhost$(NC)"

dev-build: ## Build forçado (quando há mudanças)
	@echo "$(GREEN)🔨 Rebuild completo...$(NC)"
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) build
	$(MAKE) dev

dev-fresh: ## Limpar tudo e iniciar do zero
	@echo "$(YELLOW)🧹 Limpando e reiniciando...$(NC)"
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) down --rmi all --volumes --remove-orphans
	$(MAKE) dev-build

dev-restart: ## Reiniciar containers sem rebuild
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) restart

dev-status: ## Ver status dos containers dev
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) ps

dev-logs: ## Logs em tempo real
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) logs -f

dev-stop: ## Parar ambiente dev
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) down

### ⚙️ Serviços individuais

backend: ## Rodar apenas backend Rust
	cd backend && cargo run

frontend: ## Rodar apenas frontend React
	cd frontend && npm run dev

### 🏗️ Build

build-dev: ## Build para desenvolvimento
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) build

build-prod: ## Build para produção local
	$(DOCKER_COMPOSE) -f $(COMPOSE_PROD_FILE) build

### 🚀 Produção Local

prod-local: ## Rodar produção localmente
	$(DOCKER_COMPOSE) -f $(COMPOSE_PROD_FILE) up -d

prod-local-stop: ## Parar produção local
	$(DOCKER_COMPOSE) -f $(COMPOSE_PROD_FILE) down

prod-health: ## Verificar saúde da aplicação
	@echo "$(GREEN)🏥 Health Check Backend...$(NC)"
	@curl -f http://localhost/health && echo "$(GREEN)✅ Backend OK$(NC)" || echo "$(RED)❌ Backend FAIL$(NC)"
	@echo "$(GREEN)🏥 Health Check Frontend...$(NC)"
	@curl -f http://localhost && echo "$(GREEN)✅ Frontend OK$(NC)" || echo "$(RED)❌ Frontend FAIL$(NC)"

### 🧪 Testes

test: ## Testar frontend
	cd frontend && npm test

test-backend: ## Testar backend Rust
	cd backend && cargo test

test-all: ## Testar tudo
	$(MAKE) test
	$(MAKE) test-backend

### 🔍 Lint e Format

lint: ## Lint frontend + backend
	cd frontend && npm run lint
	cd backend && cargo clippy --all-targets --all-features -- -D warnings

format: ## Format frontend + backend
	cd frontend && npm run format
	cd backend && cargo fmt

### 🖥️ Monitoramento

monitor: ## Monitorar uso do sistema
	@docker stats --no-stream

### 🛠️ Deploy (AWS)

deploy-check: ## Verificar pré-requisitos deploy
	@command -v git >/dev/null 2>&1 || { echo "$(RED)❌ Git não encontrado$(NC)"; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)❌ Docker não encontrado$(NC)"; exit 1; }
	@test -f scripts/deploy-ec2.sh || { echo "$(RED)❌ Script deploy-ec2.sh não encontrado$(NC)"; exit 1; }
	@git status --porcelain | grep -q . && { echo "$(RED)❌ Há mudanças não commitadas$(NC)"; exit 1; } || echo "$(GREEN)✅ Git limpo$(NC)"

deploy-aws: ## Deploy REAL para AWS
	$(MAKE) deploy-check
	$(MAKE) build-prod
	chmod +x scripts/deploy-ec2.sh
	./scripts/deploy-ec2.sh
	$(MAKE) deploy-verify

deploy-verify: ## Verificar se deploy foi bem-sucedido
	@sleep 10
	@curl -f https://blueprintblog.tech/health && echo "$(GREEN)✅ Deploy OK$(NC)" || echo "$(RED)❌ Deploy FAIL$(NC)"

deploy-rollback: ## Rollback
	chmod +x scripts/rollback.sh
	./scripts/rollback.sh

deploy-full: ## Deploy completo com testes
	$(MAKE) deploy-check
	$(MAKE) test-all
	$(MAKE) build-prod
	$(MAKE) deploy-aws

### 🧹 Limpeza

clean-dev: ## Limpar ambiente dev
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) down --rmi all --volumes --remove-orphans

clean-prod: ## Limpar ambiente prod local
	$(DOCKER_COMPOSE) -f $(COMPOSE_PROD_FILE) down --rmi all --volumes --remove-orphans

clean-all: ## Limpeza geral
	$(MAKE) clean-dev
	$(MAKE) clean-prod
	docker system prune -af --volumes

clean: clean-all ## Alias

### 🌐 Domínio

setup-domain: ## Setup DuckDNS
	chmod +x scripts/setup-duckdns.sh
	./scripts/setup-duckdns.sh

test-domain: ## Testar domínio
	@echo "DNS:"
	nslookup blueprintblog.tech || true
	@curl -I http://blueprintblog.tech/health || true
	@curl -I https://blueprintblog.tech/health || true

### 🐚 Shell nos containers

shell-backend: ## Shell backend
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) exec backend sh

shell-frontend: ## Shell frontend
	$(DOCKER_COMPOSE) -f $(COMPOSE_DEV_FILE) exec frontend sh

### 🔍 ENV Management

env-check: ## Ver variáveis de ambiente
	@echo "$(GREEN)🔍 Variáveis (.env):$(NC)"
	@cat .env | grep -vE '^\s*#' | grep -vE '^\s*$$' || echo "$(YELLOW)⚠️  Nenhuma variável visível$(NC)"

### 🛡️ Segurança

security-scan: ## Scan básico de imagens
	@docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep -E "(latest|none)" || echo "✅ Nenhuma imagem 'latest'"

### ℹ️ Info

info: ## Mostrar info do projeto
	@echo "Projeto: $(PROJECT_NAME)"
	@echo "Node: $(shell node --version 2>/dev/null || echo 'Não instalado')"
	@echo "Docker: $(shell docker --version 2>/dev/null || echo 'Não instalado')"

### 🛠️ Setup inicial

setup: install ## Setup inicial
	@echo "$(GREEN)🔧 Setup inicial pronto!$(NC)"

install: ## Instalar dependências front
	cd frontend && npm install

update: ## Atualizar dependências front
	cd frontend && npm update

backup-env: ## Backup .env
	cp .env .env.backup.$(shell date +%Y%m%d_%H%M%S)

# Default
.DEFAULT_GOAL := help
