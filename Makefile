# Blueprint Blog - Makefile
.PHONY: help build dev prod clean deploy logs test

# Configurações
PROJECT_NAME := blueprint-blog

# Cores para output
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

help: ## Mostrar ajuda
	@echo "$(GREEN)Blueprint Blog - Comandos Disponíveis$(NC)"
	@echo "======================================"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Desenvolvimento
dev: ## Iniciar ambiente de desenvolvimento
	@echo "$(GREEN)🚀 Iniciando ambiente de desenvolvimento...$(NC)"
	docker-compose -f docker-compose.dev.yml up --build

dev-detached: ## Iniciar ambiente de desenvolvimento em background
	@echo "$(GREEN)🚀 Iniciando ambiente de desenvolvimento (background)...$(NC)"
	docker-compose -f docker-compose.dev.yml up -d --build

dev-stop: ## Parar ambiente de desenvolvimento
	@echo "$(YELLOW)⏹️  Parando ambiente de desenvolvimento...$(NC)"
	docker-compose -f docker-compose.dev.yml down

# Build e Produção
build: ## Build da imagem Docker para produção
	@echo "$(GREEN)🔨 Fazendo build da imagem Docker...$(NC)"
	docker build -t $(PROJECT_NAME):latest .

prod: ## Rodar em modo produção local
	@echo "$(GREEN)🚀 Iniciando em modo produção...$(NC)"
	docker-compose up --build

prod-detached: ## Rodar em modo produção local (background)
	@echo "$(GREEN)🚀 Iniciando em modo produção (background)...$(NC)"
	docker-compose up -d --build

# Testes
test: ## Executar testes
	@echo "$(GREEN)🧪 Executando testes...$(NC)"
	npm test

test-docker: ## Executar testes no Docker
	@echo "$(GREEN)🧪 Executando testes no Docker...$(NC)"
	docker run --rm -v $(PWD):/app -w /app node:18-alpine npm test

# Linting e Formatação
lint: ## Executar linting
	@echo "$(GREEN)🔍 Executando linting...$(NC)"
	npm run lint

format: ## Formatar código
	@echo "$(GREEN)✨ Formatando código...$(NC)"
	npm run format

# Deploy
ec2-deploy: ## Deploy direto na instância EC2
	@echo "$(GREEN)🚀 Deploy direto na EC2...$(NC)"
	chmod +x scripts/deploy-ec2.sh
	./scripts/deploy-ec2.sh

# Domínio e DNS
setup-domain: ## Configurar domínio DuckDNS
	@echo "$(GREEN)🌐 Configurando domínio DuckDNS...$(NC)"
	chmod +x scripts/setup-duckdns.sh
	./scripts/setup-duckdns.sh

test-domain: ## Testar conectividade do domínio
	@echo "$(GREEN)🧪 Testando domínio...$(NC)"
	@echo "DNS Resolution:"
	nslookup blueprintblog.tech || true
	@echo "\nHTTP Test:"
	curl -I http://blueprintblog.tech/health || true
	@echo "\nHTTPS Test:"
	curl -I https://blueprintblog.tech/health || true

# Logs e Monitoramento
logs: ## Ver logs do container local
	@echo "$(GREEN)📋 Visualizando logs...$(NC)"
	docker-compose logs -f

status: ## Ver status dos containers
	@echo "$(GREEN)📊 Status dos containers:$(NC)"
	docker-compose ps

# Limpeza
clean: ## Limpar containers e imagens
	@echo "$(YELLOW)🧹 Limpando containers e imagens...$(NC)"
	docker-compose down --rmi all --volumes --remove-orphans
	docker system prune -f

# Utilitários
shell: ## Abrir shell no container
	@echo "$(GREEN)🐚 Abrindo shell no container...$(NC)"
	docker-compose exec $(PROJECT_NAME) sh

install: ## Instalar dependências
	@echo "$(GREEN)📦 Instalando dependências...$(NC)"
	npm install

update: ## Atualizar dependências
	@echo "$(GREEN)🔄 Atualizando dependências...$(NC)"
	npm update

security-check: ## Verificar vulnerabilidades
	@echo "$(GREEN)🔒 Verificando vulnerabilidades...$(NC)"
	npm audit

# Backup e Restore
backup-env: ## Fazer backup das variáveis de ambiente
	@echo "$(GREEN)💾 Fazendo backup das variáveis de ambiente...$(NC)"
	cp .env .env.backup.$(shell date +%Y%m%d_%H%M%S)

# Informações
info: ## Mostrar informações do projeto
	@echo "$(GREEN)📋 Informações do Projeto$(NC)"
	@echo "=========================="
	@echo "Projeto: $(PROJECT_NAME)"
	@echo "Node Version: $(shell node --version 2>/dev/null || echo 'Não instalado')"
	@echo "Docker Version: $(shell docker --version 2>/dev/null || echo 'Não instalado')"

# Comandos compostos
setup: install ## Setup inicial do projeto
	@echo "$(GREEN)🔧 Setup inicial do projeto...$(NC)"
	@echo "$(GREEN)✅ Projeto configurado com sucesso!$(NC)"

deploy-full: clean build ec2-deploy ## Deploy completo (clean + build + deploy)
	@echo "$(GREEN)🎉 Deploy completo finalizado!$(NC)"

# Default target
.DEFAULT_GOAL := help
