# Frontend (Node.js)
frontend/node_modules
frontend/dist
frontend/dist-ssr
frontend/*.local

# Backend (Rust)
backend/target/
backend/Cargo.lock
backend/.env

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Docker
.dockerignore

# Monitoring data
monitoring/data/
prometheus_data/
grafana_data/

# Uploads
backend/uploads/

# Environment files
.env
.env.local
.env.production
