version: '3.8'

services:
  # Nginx - DESENVOLVIMENTO
  nginx:
    build:
      context: .
      dockerfile: nginx.dev.Dockerfile
    ports:
      - '80:80'
    networks:
      - app-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

  # Frontend React + TypeScript (Vite) - DESENVOLVIMENTO
  frontend:
    build: ./frontend
    networks:
      - app-network
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3001/api
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend Rust - DESENVOLVIMENTO
  backend:
    build: ./backend
    ports:
      - '3001:3001'
    networks:
      - app-network
    environment:
      - DATABASE_URL=postgresql://postgres.nbzovkwvrqlxqpdpkvql:<EMAIL>:6543/postgres
      - RUST_ENV=development
      - RUST_LOG=debug
      - LOG_LEVEL=debug
      - LOG_CONSOLE=true
      - LOG_FILE=true
      - LOG_JSON=false
      - LOG_DIR=./logs
      - LOG_MAX_FILES=10
      - LOG_MAX_AGE_DAYS=7
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    # depends_on:
    #   - redis
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:3001/health || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL local removido - usando apenas Supabase

  # Redis Cache - DESABILITADO TEMPORARIAMENTE
  # redis:
  #   image: redis:7-alpine
  #   networks:
  #     - app-network
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped

# 🎯 Monitoramento removido temporariamente - foco no core: frontend + backend
# prometheus e grafana serão adicionados depois que o sistema estiver estável

# Rede única simplificada
networks:
  app-network:
    driver: bridge

# Volumes
volumes:
  frontend_dist:
  #  redis_data:
  backend_logs:
  backend_uploads:
