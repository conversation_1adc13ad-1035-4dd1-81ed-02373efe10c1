#!/bin/bash

# 🚀 Blueprint Blog - Deploy Unificado
# Uso: ./scripts/deploy.sh [comando] [opções]

set -e  # Exit on error

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funções de log
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Função de ajuda
show_help() {
    echo "🚀 Blueprint Blog - Deploy Unificado"
    echo ""
    echo "Uso: ./scripts/deploy.sh [comando] [opções]"
    echo ""
    echo "Comandos:"
    echo "  local       Deploy local (desenvolvimento)"
    echo "  aws         Deploy AWS EC2 (produção)"
    echo "  backup      Criar backup dos dados"
    echo "  duckdns     Configurar DuckDNS"
    echo "  status      Ver status dos serviços"
    echo "  logs        Ver logs dos containers"
    echo "  stop        Parar todos os serviços"
    echo "  clean       Limpar containers e imagens"
    echo ""
    echo "Opções:"
    echo "  --build     Forçar rebuild das imagens"
    echo "  --no-cache  Build sem cache"
    echo "  --help      Mostrar esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  ./scripts/deploy.sh local --build"
    echo "  ./scripts/deploy.sh aws"
    echo "  ./scripts/deploy.sh backup"
    echo "  ./scripts/deploy.sh logs backend"
}

# Verificar permissões Docker
check_docker() {
    if ! docker ps >/dev/null 2>&1; then
        log_error "Docker permission denied"
        log_info "Execute: sg docker -c './scripts/deploy.sh $*'"
        exit 1
    fi
}

# Deploy Local (Desenvolvimento)
deploy_local() {
    log_info "🏠 Deploy Local - Desenvolvimento"
    
    local build_flag=""
    if [[ "$*" == *"--build"* ]]; then
        build_flag="--build"
    fi
    
    if [[ "$*" == *"--no-cache"* ]]; then
        build_flag="--build --no-cache"
    fi
    
    log_info "🛑 Parando containers existentes..."
    docker-compose down 2>/dev/null || true
    
    log_info "🏗️ Iniciando serviços locais..."
    docker-compose up -d $build_flag
    
    log_info "⏳ Aguardando serviços..."
    sleep 20
    
    check_services_local
}

# Deploy AWS (Produção)
deploy_aws() {
    log_info "🌐 Deploy AWS EC2 - Produção"
    
    local build_flag=""
    if [[ "$*" == *"--build"* ]]; then
        build_flag="--build"
    fi
    
    if [[ "$*" == *"--no-cache"* ]]; then
        build_flag="--build --no-cache"
    fi
    
    log_info "📥 Atualizando código..."
    git pull origin main || log_warning "Falha ao atualizar código"
    
    log_info "🛑 Parando containers existentes..."
    docker-compose down 2>/dev/null || true
    
    log_info "🧹 Limpando imagens antigas..."
    docker system prune -f
    
    log_info "🏗️ Construindo e iniciando produção..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d $build_flag
    
    log_info "⏳ Aguardando serviços (45s)..."
    sleep 45
    
    check_services_aws
    show_aws_info
}

# Verificar serviços locais
check_services_local() {
    log_info "🔍 Verificando serviços locais..."
    
    if curl -f http://localhost:5173 >/dev/null 2>&1; then
        log_success "Frontend (Vite) rodando: http://localhost:5173"
    else
        log_error "Frontend não está respondendo"
    fi
    
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        log_success "Backend rodando: http://localhost:3001"
    else
        log_warning "Backend não está respondendo"
    fi
}

# Verificar serviços AWS
check_services_aws() {
    log_info "🔍 Verificando serviços AWS..."
    
    if curl -f http://localhost >/dev/null 2>&1; then
        log_success "Frontend (Nginx) rodando"
    else
        log_error "Frontend não está respondendo"
    fi
    
    if curl -f http://localhost:3001/health >/dev/null 2>&1; then
        log_success "Backend rodando"
    else
        log_warning "Backend via SSH tunnel (normal)"
    fi
}

# Mostrar informações AWS
show_aws_info() {
    local public_ip=$(curl -s ifconfig.me 2>/dev/null || echo "N/A")
    
    echo ""
    log_success "🎉 Deploy AWS concluído!"
    echo ""
    echo "📊 Acessos:"
    echo "  🌐 Frontend: http://$public_ip"
    echo "  🌐 Domínio: https://your-domain.duckdns.org"
    echo "  🔧 Backend: SSH tunnel (porta 3001)"
    echo "  📊 Grafana: SSH tunnel (porta 3000)"
    echo "  📈 Prometheus: SSH tunnel (porta 9090)"
    echo ""
    echo "🔗 Comando SSH Tunnel:"
    echo "  ssh -L 3001:localhost:3001 -L 3000:localhost:3000 -L 9090:localhost:9090 <EMAIL>"
    echo ""
}

# Backup
create_backup() {
    log_info "💾 Criando backup..."
    
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Database backup
    if docker-compose ps | grep -q "db.*Up"; then
        log_info "📊 Backup do banco de dados..."
        docker-compose exec -T db pg_dump -U blog_user blog_db > "$backup_dir/database.sql" 2>/dev/null || log_warning "Falha no backup do banco"
    fi
    
    # Files backup
    if [ -d "backend/uploads" ]; then
        log_info "📁 Backup de arquivos..."
        cp -r backend/uploads "$backup_dir/"
    fi
    
    # Config backup
    log_info "⚙️ Backup de configurações..."
    cp docker-compose.yml "$backup_dir/" 2>/dev/null || true
    cp docker-compose.prod.yml "$backup_dir/" 2>/dev/null || true
    cp -r monitoring "$backup_dir/" 2>/dev/null || true
    
    # Create archive
    log_info "🗜️ Criando arquivo..."
    tar -czf "$backup_dir.tar.gz" -C backups "$(basename "$backup_dir")" 2>/dev/null
    rm -rf "$backup_dir"
    
    log_success "Backup criado: $backup_dir.tar.gz"
}

# Configurar DuckDNS
setup_duckdns() {
    log_info "🦆 Configurando DuckDNS..."
    
    if [ -f "scripts/setup-duckdns.sh" ]; then
        chmod +x scripts/setup-duckdns.sh
        ./scripts/setup-duckdns.sh
    else
        log_error "Script setup-duckdns.sh não encontrado"
    fi
}

# Status dos serviços
show_status() {
    log_info "📊 Status dos serviços:"
    echo ""
    docker-compose ps 2>/dev/null || log_warning "Nenhum serviço rodando"
    echo ""
    
    log_info "💾 Uso de disco:"
    docker system df 2>/dev/null || true
}

# Ver logs
show_logs() {
    local service="$1"
    
    if [ -n "$service" ]; then
        log_info "📋 Logs do serviço: $service"
        docker-compose logs -f --tail=50 "$service"
    else
        log_info "📋 Logs de todos os serviços:"
        docker-compose logs -f --tail=20
    fi
}

# Parar serviços
stop_services() {
    log_info "🛑 Parando todos os serviços..."
    docker-compose down
    log_success "Serviços parados"
}

# Limpeza
clean_all() {
    log_warning "🧹 Limpeza completa (containers, imagens, volumes)..."
    read -p "Tem certeza? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v 2>/dev/null || true
        docker system prune -af --volumes
        log_success "Limpeza concluída"
    else
        log_info "Limpeza cancelada"
    fi
}

# Main
main() {
    local command="$1"
    shift || true
    
    case "$command" in
        "local")
            check_docker
            deploy_local "$@"
            ;;
        "aws")
            check_docker
            deploy_aws "$@"
            ;;
        "backup")
            check_docker
            create_backup
            ;;
        "duckdns")
            setup_duckdns
            ;;
        "status")
            check_docker
            show_status
            ;;
        "logs")
            check_docker
            show_logs "$1"
            ;;
        "stop")
            check_docker
            stop_services
            ;;
        "clean")
            check_docker
            clean_all
            ;;
        "--help"|"help"|"")
            show_help
            ;;
        *)
            log_error "Comando desconhecido: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Executar
main "$@"
