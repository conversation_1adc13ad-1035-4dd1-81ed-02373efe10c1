#!/bin/bash

# Script para configurar DuckDNS com o Load Balancer da AWS
set -e

# Configurações
DOMAIN_NAME="blueprintblog.duckdns.org"
SUBDOMAIN="blueprintblog"

# Detectar IP da instância EC2 automaticamente
if command -v curl &> /dev/null; then
    EC2_IP=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || curl -s ifconfig.me 2>/dev/null || echo "")
elif command -v wget &> /dev/null; then
    EC2_IP=$(wget -qO- http://***************/latest/meta-data/public-ipv4 2>/dev/null || wget -qO- ifconfig.me 2>/dev/null || echo "")
fi

# Fallback para IP manual se não conseguir detectar
if [ -z "$EC2_IP" ]; then
    echo -e "${YELLOW}⚠️  Não foi possível detectar o IP automaticamente${NC}"
    echo -e "${YELLOW}💡 Por favor, informe o IP público da instância EC2:${NC}"
    read -p "IP da EC2: " EC2_IP

    if [ -z "$EC2_IP" ]; then
        echo -e "${RED}❌ IP não informado. Abortando...${NC}"
        exit 1
    fi
fi

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🦆 Configuração DuckDNS para Blueprint Blog${NC}"
echo -e "${BLUE}===========================================${NC}"
echo -e "${YELLOW}📋 Configurações:${NC}"
echo -e "  Domínio: ${DOMAIN_NAME}"
echo -e "  IP EC2: ${EC2_IP}"

# Verificar se o token DuckDNS está configurado
if [ -z "$DUCKDNS_TOKEN" ]; then
    echo -e "${RED}❌ Token DuckDNS não configurado${NC}"
    echo -e "${YELLOW}💡 Configure o token DuckDNS:${NC}"
    echo -e "   export DUCKDNS_TOKEN='seu_token_aqui'"
    echo -e "   Obtenha seu token em: https://www.duckdns.org/"
    exit 1
fi

echo -e "${GREEN}✅ Token DuckDNS configurado${NC}"

# Usar IP fixo da EC2
echo -e "${YELLOW}🔍 Usando IP fixo da EC2...${NC}"
echo -e "${GREEN}✅ IP da EC2: ${EC2_IP}${NC}"

# Atualizar DuckDNS
echo -e "${YELLOW}🦆 Atualizando DuckDNS...${NC}"

DUCKDNS_URL="https://www.duckdns.org/update?domains=${SUBDOMAIN}&token=${DUCKDNS_TOKEN}&ip=${EC2_IP}"
RESPONSE=$(curl -s "${DUCKDNS_URL}")

if [ "$RESPONSE" = "OK" ]; then
    echo -e "${GREEN}✅ DuckDNS atualizado com sucesso!${NC}"
else
    echo -e "${RED}❌ Erro ao atualizar DuckDNS: ${RESPONSE}${NC}"
    exit 1
fi

# Verificar propagação DNS
echo -e "${YELLOW}🔍 Verificando propagação DNS...${NC}"
sleep 5

for i in {1..5}; do
    RESOLVED_IP=$(nslookup ${DOMAIN_NAME} ******* 2>/dev/null | grep -A1 "Name:" | tail -n1 | awk '{print $2}' | head -n1)

    if [ "$RESOLVED_IP" = "$EC2_IP" ]; then
        echo -e "${GREEN}✅ DNS propagado corretamente!${NC}"
        break
    else
        echo -e "${YELLOW}⏳ Tentativa ${i}/5 - Aguardando propagação DNS...${NC}"
        sleep 10
    fi

    if [ $i -eq 5 ]; then
        echo -e "${YELLOW}⚠️  DNS ainda não propagou completamente${NC}"
        echo -e "${YELLOW}💡 Pode levar até 24h para propagação completa${NC}"
    fi
done

# Testar conectividade
echo -e "${YELLOW}🧪 Testando conectividade...${NC}"
sleep 5

if curl -f -s "http://${DOMAIN_NAME}/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Domínio respondendo corretamente!${NC}"
elif curl -f -s "https://${DOMAIN_NAME}/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Domínio respondendo com HTTPS!${NC}"
else
    echo -e "${YELLOW}⚠️  Domínio ainda não está respondendo${NC}"
    echo -e "${YELLOW}💡 Aguarde alguns minutos para propagação completa${NC}"
fi

echo -e "\n${GREEN}🎉 Configuração DuckDNS concluída!${NC}"
echo -e "${GREEN}=================================${NC}"
echo -e "${GREEN}🌐 Domínio: ${DOMAIN_NAME}${NC}"
echo -e "${GREEN}📍 IP: ${EC2_IP}${NC}"

echo -e "\n${BLUE}📝 Próximos passos:${NC}"
echo -e "1. Aguarde a propagação DNS completa (até 24h)"
echo -e "2. Acesse: http://${DOMAIN_NAME}"
echo -e "3. Se configurou SSL, acesse: https://${DOMAIN_NAME}"
echo -e "4. Configure renovação automática do DuckDNS (opcional)"

echo -e "\n${BLUE}🔄 Renovação automática (opcional):${NC}"
echo -e "Adicione ao crontab para renovar a cada 5 minutos:"
echo -e "*/5 * * * * curl -s \"https://www.duckdns.org/update?domains=${SUBDOMAIN}&token=${DUCKDNS_TOKEN}&ip=${EC2_IP}\" > /dev/null"

echo -e "\n${GREEN}🚀 ${DOMAIN_NAME} está configurado!${NC}"
